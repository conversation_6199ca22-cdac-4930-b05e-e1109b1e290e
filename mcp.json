{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@context7/mcp-server"]}, "sequential - thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced==2.6.0"], "timeout": 120000, "autoApprove": ["interact-enhanced"]}, "Playwright": {"command": "npx @playwright/mcp@latest", "env": {}}, "mysql": {"command": "npx", "args": ["-y", "@f4ww4z/mcp-mysql-server"], "env": {"MYSQL_HOST": "sql.s1271.vhostgo.com", "MYSQL_USER": "qsdzy", "MYSQL_PASSWORD": "Ftp70701191", "MYSQL_DATABASE": "qsdzy"}}}}