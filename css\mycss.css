/*黑白网页*/
/* html { 
        -webkit-filter: grayscale(100%); 
        -moz-filter: grayscale(100%); 
        -ms-filter: grayscale(100%); 
        -o-filter: grayscale(100%); 
        filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);  
        _filter:none; 
    } 

behavior:url(border-radius.htc)*/

/* body,td,th {font-size:1em;font: normal 100% Helvetica, Arial, sans-serif;}
a {font: normal 100% Helvetica, Arial, sans-serif;color: #333333;}
body {margin-left: 0px;	margin-top: 0px;margin-right: 0px;margin-bottom: 0px;background-color: #f5f5f5;}
a:active {text-decoration: none;color: #CC0000;}
a:link{font-size:1em;color:#000000;text-decoration:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a:visited{font-size:1em;color:#000000;	text-decoration:none;}
a:hover{font-size:1em;color:#000000;text-decoration:none;}
a.white:link{font-size:1em;color:#ffffff;text-decoration:none;}
a.white:visited{font-size:1em;color:#ffffff;text-decoration:none;}
a.white:hover{font-size:1em;color:#ffffff;text-decoration:underline;}
h1 {font-size: 1em; }
p{margin:0;}*/
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;
}

body {
  background-color: #f9f9f9;
  color: #333;
}

a {
  text-decoration: none;
  color: inherit;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 5px;
  padding-bottom: 10px;
}
#page{font-size:0.9em;height:30px;padding:3px 3px;}
#page a{display:block;float:left;margin-right:2px;padding:3px 10px;height:30px;border:1px #cccccc solid;background:#ffffff;text-decoration:none;color:#000000;font-size:1em;line-height:20px;border-radius: 5px;}
#page a:hover{color:#3eb1dd;border:1px #d5d5d5 solid;}
#page a.cur{border:none;background:#3eb1dd;border:1px #3eb1dd solid;color:#ffffff;}
#page p{float:left;padding:3px 10px;font-size:1em;height:30px;line-height:20px;color:#bbbbbb;border:1px #cccccc solid;background:#efefef;margin-right:2px;border-radius: 5px;}
#page p.pageRemark{border-style:none;background:none;margin-right:0px;padding:2px 0px;color:#666;}
#page p.pageRemarkb{color:red;}
#page p.pageEllipsis{border-style:none;background:none;padding:2px 0px;color:#3eb1dd;}
.bottr{float:right;} 
.pagecenter{width: 355px;overflow: hidden; margin:0 auto ;text-align:center;padding-top:10px;padding-bottom:10px;}  

.fdBonTel{max-width: 600px;width:100%;font-size:0.8em; height:55px; position:fixed; background:#fff; text-align:center; left: 50%; transform: translateX(-50%);bottom:0; z-index:999;box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); }
.titlebar{max-width: 600px;width:100%;position:fixed;top:0px;left: 50%; transform: translateX(-50%);  text-align:center; z-index:999;background-color: #3eb1dd;}
.titlebarword{width:100%; height:40px; position:fixed;  text-align:center; left:0; top:0px; z-index:999;}
.titlebarfl{max-width: 600px;width:100%;position:fixed;top:0px;left: 50%; transform: translateX(-50%);  text-align:center; background-color: #3eb1dd;top:40px; z-index:999;}
.titlebar1{width:250px; height:40px; position:fixed; text-align:center;top:0;left:0;right:0;margin:auto;z-index:999;}
.wxzz{background:#000000;filter:alpha(Opacity=90);-moz-opacity:0.9;opacity: 0.9;left:0;top:0;position:fixed;height:100%;width:100%;overflow:hidden;z-index:10000;}

.userinfoset_select{height:30px;border: solid 0px;width:95%;}
.userinfoset_input{border: solid 0px;height:30px;}

.inputg{height:21px;border:1px #e0e0e0 solid;border-radius: 2px;}
.inputk{width:95%;}
.inputk100{width:100%;}
.inputlong{width:80%;height:30px;border:1px #3eb1dd solid;border-radius: 2px;}
.btn{display: inline-block;margin-top: 0px;padding: 12px 12px;border-radius: 5px;background-color: #3eb1dd;color: #ffffff;cursor: pointer;}
.btns{display: inline-block;margin-top: 0px;padding: 3px 8px;border-radius: 5px;background-color: #3eb1dd;color: #ffffff;cursor: pointer;}
.btnlong{width:80%;color:#ffffff;border:1px  #3eb1dd solid;border-radius: 5px;background-color: #3eb1dd;padding: 12px 12px;}
/*.btnlong:hover{background-color: #3eb1dd;}
.btnlong:active{background-color: #3eb1dd;}
.btn:hover{background-color: #3eb1dd;}
.btn:active{background-color: #3eb1dd;}*/
.titlebtn{width:98%;padding: 4px 4px;background-color: #ffffff;}
.titlebtn:hover{background-color: #ffffff;}
.btnrmzy{display:inline-block;width:100%;padding:0px;background-color: #ffffff;cursor:pointer;}
.btnrmzy:hover{background-color: #ffffff;}
.titlebtn1{background-color: #efefef;}

.inbtn{border: none;}
.bubtn{border: none;}
.bgbtn02 img{margin-bottom: -9px;margin-right: 8px;}
.btnbg{width:79px;background: url(images/btnbg.png) no-repeat center;margin-bottom: -9px;margin-right: 8px;}
.btn_xan{color:#ffffff;border:1px #3eb1dd solid;border-radius: 15px;background-color: #3eb1dd;padding: 2px 8px 2px 8px;}

.myusername{
    background: url(images/username.png)no-repeat 10px ;
    width: 230px;
    height: 35px;
    border-radius:5px;
    border: 1px solid #3eb1dd;
    box-shadow: 1px 1px 3px #cccccc;
    padding-left: 40px;
}
.myloginbtn{width:270px;height: 42px;color:#ffffff;	border:1px #3eb1dd solid;border-radius: 5px;background-color: #3eb1dd;box-shadow: 1px 1px 3px #cccccc;}
.tabradl{border-top-left-radius:5px;border-bottom-left-radius:5px;overflow:hidden;}
.tabradr{border-top-right-radius:5px;border-bottom-right-radius:5px;overflow:hidden;}
.tabrad{border-radius:10px 10px 10px 10px;overflow:hidden;}
.tabradt{border-radius:10px 10px 0px 0px;overflow:hidden;}
.tabyy{box-shadow: 1px 1px 1px #ffffff;}

.titlebarfont{text-shadow: 0px 1px 0px #000000;color:#efefef;font-weight: bold;}

.right-bottom
{
	width: 35px;
	height:35px;
	position: fixed;/*这是必须的*/
	z-index: 999;
	right: 15px;/*这是必须的*/
	bottom: 70px;;/*这是必须的*/
	background:transparent;
	font-size:0.8em;
	color:#3eb1dd;
	text-align:center;
  display: none;
  cursor: pointer;
}

.circle {
border-radius: 50%;
width: 100px;
height: 100px; 
line-height:100px;  
text-align:center;
margin: 0 auto;
/* 画圆宽度和高度需要相等 */
}

.arrow { display: inline-block; width: 0px; height: 0px; border:8px solid transparent; overflow: hidden; position: relative;}
/*向上箭头，只有三个边，不能指定上边框*/
.arrow-up { border-top: none; border-bottom-color: white; }
.arrow-up-blue { border-top: none; border-bottom-color: #3eb1dd; }
/*向下箭头 ，只有三个边，不能指定下边框*/
.arrow-down { border-bottom: none; border-top-color: white; }
/*向左的箭头：只有三个边，不能指定左边框，向左三角形的高=上+下边框的长度。宽=右边框的长度*/
.arrow-left { border-left: none; border-right: 40px solid white; }
/*向右的箭头：只有三个边，不能指定右边框。向右三角形的高=上+下边框的长度。宽=左边框的长度*/
.arrow-right { border-right: none; border-left: 40px solid white; }

 /*圆形图片*/
#div_tx img{
	border-radius:50%;
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	border: 3px solid #e5e5e5;
	overflow: hidden;
}
.ai-fl{
	font-size:13px;
	height:25px;
	width:21%;
	line-height:25px;
	text-align: center;
	border-radius:5px;
	background-color:#fff;
	margin: 2px;
	padding:2px;
	display:inline-block;
}

/*文本显示美化行数，超出显示...*/
.text-1h {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.text-2h {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.text-3h {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.text-zbt {
  color: #000;
  font-weight: bold;
  line-height: 25px;
}

.text-fbt {
  color: #a0a0a0;
  font-size: 0.7em;
  line-height: 15px;
}

.text-head {
  color: #000;
  font-size: 16px;
  font-weight: bold;
  line-height: 25px;
}

.text-detail {
  color: #a0a0a0;
  font-size: 12px;
  line-height: 15px;
}

.input-qianzui {
  display: block;
  height: 35px;
  margin: 5px 0px;
  padding: 0px;
  line-height: 35px;
  background: #f8f8f8;
  border-radius: 1px;
  border: 1px solid #efefef;
  cursor: pointer;
  text-align: center;
}
  /*
   * 基于复选框和单选框的卡片风格多选组件
   * 需要具备一些基础的 CSS 技能，以下样式均为外部自主实现。
   */
  /* 主体 */
  .layui-form-checkbox>.lay-skin-checkcard,
  .layui-form-radio>.lay-skin-checkcard {
    /* display: table; */
    display: flex;
	  height: 30px;
    padding: 0px 6px;
    white-space: normal;
    border-radius: 3px;
    border: 1px solid #e5e5e5;
    color: #000;
    background-color: #fff;
    text-align: center;
    align-items: center;
  }
  .layui-form-checkbox>.lay-skin-checkcard>*,
  .layui-form-radio>.lay-skin-checkcard>* {
    /* display: table-cell; */  /* IE */
    vertical-align: middle;
  }
  /* 悬停 */
  .layui-form-checkbox:hover>.lay-skin-checkcard,
  .layui-form-radio:hover>.lay-skin-checkcard {
    border-color: #16b777;
  }
  /* 选中 */
  .layui-form-checked>.lay-skin-checkcard,
  .layui-form-radioed[lay-skin="none"]>.lay-skin-checkcard {
    color: #000;
    border-color: #16b777;
    background-color: rgb(22 183 119 / 10%) !important;
    /* box-shadow: 0 0 0 3px rgba(22, 183, 119, 0.08); */
  }
  /* 禁用 */
  .layui-checkbox-disabled>.lay-skin-checkcard,
  .layui-radio-disabled>.lay-skin-checkcard {
    box-shadow: none;
    border-color: #e5e5e5 !important;
    background-color: #eee !important;
  }
  /* card 布局 */
  .lay-skin-checkcard-avatar {
    padding-right: 8px;
  }
  .lay-skin-checkcard-detail {
    overflow: hidden;
    width: 100%;
  }
  .lay-skin-checkcard-header {
    font-weight: 500;
    font-size: 16px;
    white-space: nowrap;
    margin-bottom: 4px;
  }
  .lay-skin-checkcard-description {
    font-size: 14px;
    color: #5f5f5f;
  }
  .layui-disabled  .lay-skin-checkcard-description{
    color: #c2c2c2! important;
  }
  /* 选中 dot */
  .layui-form-checked>.lay-check-dot:after,
  .layui-form-radioed>.lay-check-dot:after {
    position: absolute;
    content: "";
    top: 0px;
    right: 0px;
    width: 0;
    height: 0;
    display: inline-block;
    vertical-align: middle;
    border-width: 10px;
    border-style: dashed;
    border-color: transparent;
    border-top-left-radius: 0px;
    border-top-right-radius: 1px;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 1px;
    border-top-color: #16b777;
    border-top-style: solid;
    border-right-color: #16b777;
    border-right-style: solid;
    overflow: hidden;
  }
  .layui-checkbox-disabled>.lay-check-dot:after,
  .layui-radio-disabled>.lay-check-dot:after {
    border-top-color: #d2d2d2;
    border-right-color: #d2d2d2;
  }
  /* 选中 dot-2 */
  .layui-form-checked>.lay-check-dot-2:before,
  .layui-form-radioed>.lay-check-dot-2:before {
    position: absolute;
    font-family: "layui-icon";
    content: "\e605";
    color: #fff;
    bottom: 2px;
    right: 1px;
    font-size: 9px;
    z-index: 12;
  }
  .layui-form-checked>.lay-check-dot-2:after,
  .layui-form-radioed>.lay-check-dot-2:after {
    position: absolute;
    content: "";
    bottom: 0px;
    right: 0px;
    width: 0;
    height: 0;
    display: inline-block;
    vertical-align: middle;
    border-width: 10px;
    border-style: dashed;
    border-color: transparent;
    border-top-left-radius: 1px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 1px;
    border-bottom-left-radius: 0px;
    border-right-color: #16b777;
    border-right-style: solid;
    border-bottom-color: #16b777;
    border-bottom-style: solid;
    overflow: hidden;
  }
  .layui-checkbox-disabled>.lay-check-dot-2:before,
  .layui-radio-disabled>.lay-check-dot-2:before {
    color: #eee !important;
  }
  .layui-checkbox-disabled>.lay-check-dot-2:after,
  .layui-radio-disabled>.lay-check-dot-2:after {
    border-bottom-color: #d2d2d2;
    border-right-color: #d2d2d2;
  }
  .lay-ellipsis-multi-line {
    overflow: hidden;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  /* <!-- 自定义方框风格 --> */
    .layui-form-radio>.lay-skin-zdy,
    .layui-form-checkbox>.lay-skin-zdy {
        border-radius: 3px;
        height: 30px;
        line-height: 30px;
        padding: 0px 6px;
        border: 1px solid #ccc;
        color: #999 !important;
        position: relative;
        text-align: center;
        align-items: center;
    }

    .layui-form-checked>.lay-skin-zdy,
    .layui-form-radioed>.lay-skin-zdy {
        color: #000 !important;
        border-color: #16b777;
        background-color: rgb(22 183 119 / 10%) !important;
    }

    .layui-form-checked>.lay-skin-zdy::after,
    .layui-form-radioed>.lay-skin-zdy::after {
        content: "\e605";        /* Layui 图标字体 layui-icon-ok */
        font-family: "layui-icon";
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;        /* 可根据实际情况调整，影响勾的位置和背景大小 */
        height: 16px;        /* 可根据实际情况调整，影响勾的位置和背景大小 */
        background: linear-gradient(to top left, #52c41a 50%, transparent 50%);
        color: white;
        font-size: 9px;        /* 可根据实际情况调整，影响勾的大小 */
        text-align: right;
        line-height: 24px;        /* 与高度一致，使勾垂直居中 */
        padding-right: 0px;        /* 可根据实际情况调整，影响勾与右边的距离 */
        padding-bottom: 0px;        /* 可根据实际情况调整，影响勾与底边的距离 */
    }
    .checkAll-button {
      /*全选框美化*/
      width: 64px;
      display: inline-block;
      margin-left: 8px;
      line-height: 30px;
      color: #fff;
      height: 30px;
      padding: 0px 6px;
      white-space: normal;
      border-radius: 3px;
      border: 0px solid #16b777;
      background-color: #16b777;
      text-align: center;
      align-items: center;
    }
  /* 提示样式 */
.ts {
    margin: 25px 10px;
    line-height: 22px;
    
}
.ts .title {
    font-weight: bold;
    position: relative;
    padding-left: 10px;
    margin-bottom: 5px;
}

.ts .title::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 1em;
    background-color: #3eb1dd;
    border-radius: 3px;        
}
.ts .nr{
    color:#a0a0a0;
    /* text-indent: 2em; */
}
 
/* 标题前面的小竖条提示样式 */
.blue-bullet {
    border: 2px solid #3eb1dd;
    margin-right: 2px;
    margin-bottom: 2px;
    height: 1em;
    display: inline-block;
    vertical-align: middle;
    border-radius: 2px;
}
.green-bullet {
  border: 2px solid #16baaa;
  margin-right: 5px;
  margin-bottom: 2px;
  height: 1em;
  display: inline-block;
  vertical-align: middle;
  border-radius: 2px;
}