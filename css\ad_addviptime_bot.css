@charset "utf-8";

ul,ol,li{
	list-style: none;
	margin: 0;
	padding: 0;
}

.alertDisable{
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	display: none;
	background-color: rgba(0,0,0,0.7);
	filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7F000000,endcolorstr=#7F000000);
	z-index: 999;
}
.alertBox{
	width: 100%;
	height: 16rem;
	position: absolute;
	/*top: 20%;*/
	bottom: 0rem;
	left: 0rem;
	margin-left: 0rem;
	border-radius: 1rem 1rem  0rem 0rem;
	overflow: hidden;
	background-color: #fff;
	/*上滑效果*/
    
}
 @keyframes alertBoxIn {
	 from {
			opacity: 0;
			transform: translateY(100%);
		}
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  /* 定义消失动画 */
@keyframes alertBoxOut {
    from {
        opacity: 1;
		transform: translateY(0);
    }
    to {
        opacity: 0;
		transform: translateY(100%);
    }
}
.alertHeader{
	font-size: 1.1rem;
	height: 2.5rem;
	line-height: 2.5rem;
	text-align: center;
	font-weight: bolder;
	color: #fff;
	letter-spacing: 0.2rem;/*文字间距*/
	background-color: #3eb1dd;
}
.alertContent{
	height: 7rem;
	overflow-y: auto;
	padding: 1rem;
	color: #333;
}
.alertContent p{
	font-size: 1rem;
	letter-spacing: 0.2rem;/*文字间距*/
	word-break:break-all;
	text-indent: 2.4rem;
	text-align: left;
}
.alertFooter{
	height: 3.5rem;
	text-align: center;
	line-height: 3.5rem;
	/*border-top: 1px solid #3eb1dd;
	/*background-color: #ccc;*/
	font-size: 0;
}
.alertFooter a{
	display: inline-block;
	vertical-align: middle;
	text-align: center;
	text-decoration: none;
	cursor: pointer;
	width: 40%;
	height: 2.5rem;
	line-height: 2.5rem;	
	color: #000;
	font-size: 1rem;
	border-radius: 0.3rem;
	background-color: #fff;
}
.alertFooter a.alertOK{
	background-color: #3eb1dd;
	color: #fff;
	/*margin-right: 5%;*/
}
.alertFooter a.alertCancle{
	border: 1px solid #3eb1dd;
	box-sizing: border-box;
	color: #ff0000;
	/*margin-left: 5%;*/
}
.alertFooter a:hover{
	text-decoration: none;
	/*background-color: #777;*/
}
.close-icon {
  position: relative;
  width: 20px;
  height: 20px;
  left: 94%;
  top: -18px; /* 向上偏移10像素 */
  font-weight: bolder;
}
.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
}
 .close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}
 .close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.addviptime_close{
  position: absolute;
  background-image: url('../images/close.png');
  background-size: cover;
  background-position: center;
  width: 25px;
  height: 25px; /* 设置你想要的高度 */
  right: 15px;
  top: 8px; /* 向上偏移10像素 */
}