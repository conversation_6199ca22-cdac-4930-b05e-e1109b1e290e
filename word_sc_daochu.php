<?php

/**
 * 收藏数据导出功能页面（简化版）
 * 文件名：word_sc_daochu.php
 * 创建时间：2024年12月19日
 * 重构时间：2024年12月19日
 * 
 * 使用新的单表word_sc结构
 * 简化导出逻辑，支持Excel格式导出
 * 直接显示和导出所有收藏数据，无需筛选
 * 
 * 性能优化版本 - 2024年12月19日
 * 1. 使用批量查询减少数据库访问次数
 * 2. 添加加载进度显示
 * 3. 优化数据获取逻辑
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 处理AJAX请求 - 必须在任何HTML输出之前
if (isset($_POST['load_preview']) && $_POST['load_preview'] === 'true') {
    // 只包含必要的文件
    include("conn.php");
    include("checkvip.php");

    // 检查用户登录状态
    if (!isset($_COOKIE['loginid']) || empty($_COOKIE['loginid'])) {
        echo json_encode(['success' => false, 'msg' => '用户未登录']);
        exit;
    }

    try {
        // 简化的测试版本
        $debug_info = [];
        $debug_info[] = "开始加载预览数据，用户ID: " . $_COOKIE['loginid'];
        
        // 先测试基本查询
        $sql = "SELECT COUNT(*) as count FROM word_sc WHERE 用户ID = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("SQL预处理失败: " . $conn->error);
        }
        
        $stmt->bind_param("i", $_COOKIE['loginid']);
        if (!$stmt->execute()) {
            throw new Exception("SQL执行失败: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $count = $row['count'];
        
        $debug_info[] = "收藏数据查询成功，总记录数: " . $count;
        
        if ($count == 0) {
            echo json_encode([
                'success' => true,
                'data' => [],
                'debug' => $debug_info
            ]);
            exit;
        }
        
        // 加载基本的收藏数据
        $sql_data = "SELECT 
                        ws.科类, 
                        ws.批次, 
                        ws.院校名称, 
                        ws.专业组名称, 
                        ws.专业名称, 
                        ws.计划数, 
                        ws.选科要求,
                        ws.收藏年份,
                        ws.收藏时间
                    FROM word_sc ws
                    WHERE ws.用户ID = ? 
                    ORDER BY 
                        CASE ws.科类
                            WHEN '物理类' THEN 1
                            WHEN '历史类' THEN 2
                            ELSE 3
                        END,
                        CASE ws.批次
                            WHEN '提前本科' THEN 1
                            WHEN '本科' THEN 2
                            WHEN '提前专科' THEN 3
                            WHEN '专科' THEN 4
                            ELSE 5
                        END,
                        ws.专业组名称 ASC,
                        ws.院校名称 ASC,
                        ws.专业名称 ASC
                    LIMIT 100"; // 限制加载数量，避免性能问题
        
        $stmt_data = $conn->prepare($sql_data);
        if (!$stmt_data) {
            throw new Exception("数据查询预处理失败: " . $conn->error);
        }
        
        $stmt_data->bind_param("i", $_COOKIE['loginid']);
        if (!$stmt_data->execute()) {
            throw new Exception("数据查询执行失败: " . $stmt_data->error);
        }
        
        $result_data = $stmt_data->get_result();
        $all_data = [];
        
        while ($row = $result_data->fetch_assoc()) {
            $college_name = $row['院校名称'];
            
            // 为每条记录添加基本信息
            $item = array_merge($row, [
                '院校信息' => '院校信息加载中...',
                '排名信息' => '排名信息加载中...',
                '投档线信息' => '投档线信息加载中...',
                '招生计划信息' => '招生计划信息加载中...'
            ]);
            
            // 尝试获取院校基本信息（使用简单的查询方式）
            $sql_yx = "SELECT 省份名称, 类型, 层次, 办学性质, f211, f985, 双一流, 中央部属 FROM yxinfo WHERE 院校名称 = ? LIMIT 1";
            $stmt_yx = $conn->prepare($sql_yx);
            if ($stmt_yx) {
                $stmt_yx->bind_param("s", $college_name);
                if ($stmt_yx->execute()) {
                    $result_yx = $stmt_yx->get_result();
                    $yx_row = $result_yx->fetch_assoc();
                    if ($yx_row) {
                        $info_parts = [];
                        if (!empty($yx_row['省份名称'])) $info_parts[] = $yx_row['省份名称'];
                        if (!empty($yx_row['类型'])) $info_parts[] = $yx_row['类型'];
                        if (!empty($yx_row['层次'])) $info_parts[] = $yx_row['层次'];
                        if (!empty($yx_row['办学性质'])) $info_parts[] = $yx_row['办学性质'];
                        
                        // 院校特征
                        $features = [];
                        if (isset($yx_row['f985']) && intval($yx_row['f985']) == 1) $features[] = '985';
                        elseif (isset($yx_row['f211']) && intval($yx_row['f211']) == 1) $features[] = '211';
                        if (!empty($yx_row['双一流'])) $features[] = $yx_row['双一流'];
                        if ($yx_row['中央部属'] == '1') $features[] = '部委直属';
                        
                        if (!empty($features)) {
                            $info_parts[] = implode(' ', $features);
                        }
                        
                        $item['院校信息'] = implode(' | ', $info_parts);
                    }
                }
                $stmt_yx->close();
            }
            
            // 尝试获取排名信息
            $sql_rk = "SELECT 排名类型, 排名, 分类 FROM pm_rk WHERE 院校名称_rkpm = ? ORDER BY 排名 ASC LIMIT 5";
            $stmt_rk = $conn->prepare($sql_rk);
            if ($stmt_rk) {
                $stmt_rk->bind_param("s", $college_name);
                if ($stmt_rk->execute()) {
                    $result_rk = $stmt_rk->get_result();
                    $rankings = [];
                    while ($rk_row = $result_rk->fetch_assoc()) {
                        if (!empty($rk_row['排名']) && !empty($rk_row['排名类型'])) {
                            $ranking = $rk_row['排名类型'];
                            if (!empty($rk_row['分类'])) {
                                $ranking .= '(' . $rk_row['分类'] . ')';
                            }
                            $ranking .= ':' . $rk_row['排名'];
                            $rankings[] = $ranking;
                        }
                    }
                    
                    if (!empty($rankings)) {
                        $item['排名信息'] = implode('<br>', $rankings);
                    } else {
                        $item['排名信息'] = '暂无排名信息';
                    }
                } else {
                    $item['排名信息'] = '排名信息查询失败';
                }
                $stmt_rk->close();
            } else {
                $item['排名信息'] = '排名信息查询预处理失败';
            }
            
            // 获取投档线信息（参考zysx_x_functions.php的实现）
            $item['投档线信息'] = getToudangInfoMultiYears($conn, $college_name, $row['科类'], $row['批次']);
            
            // 获取招生计划信息（参考zysx_x_functions.php的实现）
            $item['招生计划信息'] = getZsjhInfoMultiYears($conn, $college_name, $row['科类'], $row['批次']);
            
            $all_data[] = $item;
        }
        
        $debug_info[] = "成功加载 " . count($all_data) . " 条收藏记录";
        
        echo json_encode([
            'success' => true,
            'data' => $all_data,
            'debug' => $debug_info,
            'message' => '数据加载成功，详细信息将在后续版本中逐步完善'
        ]);
        
    } catch (Exception $e) {
        $debug_info[] = "加载数据失败: " . $e->getMessage();
        echo json_encode([
            'success' => false,
            'msg' => '加载数据失败: ' . $e->getMessage(),
            'debug' => $debug_info
        ]);
    }
    exit;
}

// 首先处理导出请求，必须在任何HTML输出之前
if (isset($_POST['export'])) {
    // 包含必要的文件但不输出HTML
    include("conn.php");
    include("checkvip.php");

    // 检查用户登录状态
    if (!isset($_COOKIE['loginid']) || empty($_COOKIE['loginid'])) {
        echo '<script>alert("用户未登录，无法导出数据"); history.back();</script>';
        exit;
    }

    if ($_POST['export'] === 'excel') {
        exportToExcel();
        exit;
    }
}

try {
    include "htmlhead.php";
    include("conn.php");
    include("checkvip.php");

    // 检查用户登录状态
    if (!isset($_COOKIE['loginid']) || empty($_COOKIE['loginid'])) {
        echo '<div style="text-align:center;font-size:1.5em;color:#a0a0a0;line-height:22px;"><br><br><br>您还未<a href="login.php" style="color:#3eb1dd;">登录</a>，无法使用该功能！</div>';
        include("dhtool.php");
        exit;
    }

    $titlebarnr = "导出收藏数据";
    include("titlebar.php");

    // 页面加载时不获取预览数据
    $previewData = [];
} catch (Exception $e) {
    echo "系统错误: " . $e->getMessage();
    exit;
}
?>

<!-- 引入收藏功能样式 -->
<link rel="stylesheet" href="css/word_sc.css">

<!-- 导出提示框样式 -->
<style>
/* 导出加载提示框样式 */
.export-loading-skin .layui-layer-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.export-loading-skin .layui-layer-content #exportLoadingContent {
    background: rgba(255,255,255,0.95);
    border-radius: 8px;
    margin: 10px;
    color: #333;
}

/* 导出按钮加载状态样式 */
.favorite-action-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.favorite-action-btn:disabled .layui-icon-loading {
    animation: layui-rotate 1s linear infinite;
}

@keyframes layui-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 导出进度条样式 */
.export-progress-bar {
    width: 100%;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    margin-top: 10px;
    overflow: hidden;
}

.export-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1E9FFF, #5FB3F5);
    border-radius: 2px;
    transition: width 0.3s ease;
}
</style>



<div class="layui-container">
    <!-- 数据预览区域 -->
    <div class="filter-section">
        <!-- 导出按钮 -->
        <div class="export-button-container">
            <button type="button" id="toggleMajorInfo" class="favorite-action-btn favorite-btn-secondary">
                <i class="layui-icon layui-icon-eye"></i> 显示专业信息
            </button>
            <button type="button" id="togglePreviewTable" class="favorite-action-btn">
                <i class="layui-icon layui-icon-table"></i> 志愿预览
            </button>
            <form method="post" action="" style="display: inline;" id="excelExportForm">
                <button type="submit" name="export" value="excel" class="favorite-action-btn favorite-btn-primary" id="exportExcelBtn">
                    <i class="layui-icon layui-icon-export"></i> 导出Excel
                </button>
            </form>

        </div>

        <!-- 预览表格 -->
        <div class="preview-table-container" id="previewTableContainer" style="display: none;">
            <!-- 表格标题 -->
            <div class="table-title">
                <h4>
                    <?php echo date('Y'); ?>年高考志愿表<br>
                </h4>
                <h3>
                    <?php if (isset($user_logged_in) && $user_logged_in): ?>
                        （科类：<?php echo htmlspecialchars($dl_bkkl ?? '未设置'); ?>、
                        选科：<?php echo htmlspecialchars($dl_xk ?? '未设置'); ?>、
                        分数：<?php echo htmlspecialchars($dlgkfs ?? '未设置'); ?>、
                        排名：<?php echo htmlspecialchars($dlspm ?? '未设置'); ?>）
                    <?php endif; ?>
                </h3>               
                    <span style="color: #ff0000; font-size: 14px; font-weight: bold;">
                        注：投档信息中平均分和排名是指所有专业组平均投档分和投档排名
                    </span>
            </div>
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>科类</th>
                        <th>批次</th>
                        <th>院校名称</th>
                        <th>院校信息</th>
                        <th>排名信息</th>
                        <th>投档线信息</th>
                        <th>招生计划信息</th>
                        <th class="major-info-column">专业组名称</th>
                        <th class="major-info-column">专业名称</th>
                        <th class="major-info-column">计划数</th>
                        <th class="major-info-column">选科要求</th>
                    </tr>
                </thead>
                <tbody id="previewTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 导出说明 -->
    <div class="filter-section" style="margin-top: 10px;">
        <h3><span class="green-bullet"></span>导出说明：</h3>
        <div class="export-info" style="margin-left: 10px;">
            <p><strong>预览表格分为两类显示：</strong></p>
            <ul style="margin-left: 20px; margin-bottom: 10px;">
                <li><strong>院校信息</strong>：科类、批次、院校名称、院校信息（省份|类型|层次|办学性质|特征）、排名信息、投档线信息（近三年最高|最低|平均分数及排名）、招生计划信息（近三年总计划数）</li>
                <li><strong>专业信息</strong>：专业组名称、专业名称、计划数、选科要求</li>
            </ul>
            <p><strong>文件格式：</strong></p>
            <ul style="margin-left: 20px; margin-bottom: 10px;">
                <li><strong>Excel格式 (.xls)</strong>：支持表格样式、边框、颜色等，可用Excel直接打开，适合查看和打印</li>
            </ul>
            <p><strong>排序规则：</strong>按科类（物理类→历史类→其他）、批次（提前本科→本科→提前专科→专科）、同科类批次按投档线平均分降序、分数相同按排名升序</p>
        </div>
    </div>
</div>

<?php

/**
 * 检查表是否存在的辅助函数
 */
function tableExists($conn, $tableName) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    return mysqli_num_rows($result) > 0;
}

/**
 * 获取预览数据（优化版本）
 * 使用批量查询减少数据库访问次数
 */
function getPreviewDataOptimized($debug_info = [])
{
    global $conn, $dlid;

    try {
        // 调试信息
        $debug_info[] = "getPreviewDataOptimized开始执行，用户ID: " . $dlid;
        $debug_info[] = "数据库连接状态: " . ($conn ? '已连接' : '未连接');
        
        // 检查必要变量
        if (!$conn) {
            throw new Exception("数据库连接失败");
        }
        
        if (!$dlid) {
            throw new Exception("用户ID未设置");
        }

        // 1. 获取所有收藏数据
        $sql_sc = "SELECT 
                        ws.科类, 
                        ws.批次, 
                        ws.院校名称, 
                        ws.专业组名称, 
                        ws.专业名称, 
                        ws.计划数, 
                        ws.选科要求,
                        ws.收藏年份,
                        ws.收藏时间
                    FROM word_sc ws
                    WHERE ws.用户ID = ? 
                    ORDER BY 
                        CASE ws.科类
                            WHEN '物理类' THEN 1
                            WHEN '历史类' THEN 2
                            ELSE 3
                        END,
                        CASE ws.批次
                            WHEN '提前本科' THEN 1
                            WHEN '本科' THEN 2
                            WHEN '提前专科' THEN 3
                            WHEN '专科' THEN 4
                            ELSE 5
                        END,
                        ws.专业组名称 ASC,
                        ws.院校名称 ASC,
                        ws.专业名称 ASC";

        $debug_info[] = "执行收藏数据查询: " . $sql_sc;
        
        $stmt_sc = $conn->prepare($sql_sc);
        if (!$stmt_sc) {
            throw new Exception("SQL预处理失败: " . $conn->error);
        }
        $stmt_sc->bind_param("i", $dlid);
        if (!$stmt_sc->execute()) {
            throw new Exception("SQL执行失败: " . $stmt_sc->error);
        }
        $result_sc = $stmt_sc->get_result();
        if (!$result_sc) {
            throw new Exception("获取收藏数据结果集失败: " . $stmt_sc->error);
        }
        $all_sc_data = [];
        while ($row = $result_sc->fetch_assoc()) {
            $all_sc_data[] = $row;
        }
        
        $debug_info[] = "收藏数据查询成功，获取到 " . count($all_sc_data) . " 条记录";

        // 如果没有收藏数据，直接返回空数组
        if (empty($all_sc_data)) {
            $debug_info[] = "用户没有收藏数据";
            return [];
        }

        // 2. 获取所有院校信息
        // 先获取收藏的院校名称列表
        $college_names = array_unique(array_column($all_sc_data, '院校名称'));
        $college_list = array_map(function($name) {
            return "'" . addslashes($name) . "'";
        }, $college_names);
        
        if (!empty($college_list)) {
            $sql_yx = "SELECT 
                            yx.院校名称,
                            yx.省份名称,
                            yx.类型,
                            yx.层次,
                            yx.办学性质,
                            yx.双一流,
                            yx.中央部属,
                            yx.f211,
                            yx.f985,
                            yx.双高计划,
                            yx.学院层次,
                            yx.主管部门,
                            yx.保研率
                        FROM yxinfo yx
                        WHERE yx.院校名称 IN (" . implode(',', $college_list) . ")";

            $debug_info[] = "执行院校信息查询: " . $sql_yx;
            
            $result_yx = mysqli_query($conn, $sql_yx);
            if (!$result_yx) {
                throw new Exception("院校信息查询失败: " . mysqli_error($conn));
            }
            $all_yx_data = [];
            while ($row = mysqli_fetch_assoc($result_yx)) {
                $all_yx_data[] = $row;
            }
        } else {
            $all_yx_data = [];
        }
        
        $debug_info[] = "院校信息查询成功，获取到 " . count($all_yx_data) . " 条记录";

        // 3. 获取所有排名信息
        // 先获取收藏的院校名称列表
        $college_names = array_unique(array_column($all_sc_data, '院校名称'));
        $college_list = array_map(function($name) {
            return "'" . addslashes($name) . "'";
        }, $college_names);
        
        if (!empty($college_list)) {
            $sql_rk = "SELECT 
                            yx.院校名称,
                            pm.排名类型,
                            pm.排名,
                            pm.分类
                        FROM pm_rk pm
                        JOIN yxinfo yx ON pm.院校名称_rkpm = yx.院校名称
                        WHERE yx.院校名称 IN (" . implode(',', $college_list) . ")
                        ORDER BY yx.院校名称 ASC, pm.排名 ASC";

            $debug_info[] = "执行排名信息查询: " . $sql_rk;
            
            $result_rk = mysqli_query($conn, $sql_rk);
            if (!$result_rk) {
                throw new Exception("排名信息查询失败: " . mysqli_error($conn));
            }
            $all_rk_data = [];
            while ($row = mysqli_fetch_assoc($result_rk)) {
                $all_rk_data[] = $row;
            }
        } else {
            $all_rk_data = [];
        }
        
        $debug_info[] = "排名信息查询成功，获取到 " . count($all_rk_data) . " 条记录";

        // 4. 获取所有投档线信息（使用zysx_x_functions.php中的函数）
        $all_td_data = [];
        $college_names = array_unique(array_column($all_sc_data, '院校名称'));
        
        foreach ($college_names as $college_name) {
            foreach ($all_sc_data as $sc_item) {
                if ($sc_item['院校名称'] === $college_name) {
                    $subject = $sc_item['科类'];
                    $batch = $sc_item['批次'];
                    
                    // 使用zysx_x_functions.php中的函数获取投档线信息
                    $toudang_info = getToudangInfoMultiYears($conn, $college_name, $subject, $batch);
                    if ($toudang_info && $toudang_info !== '该院校在该科类、批次下无投档线数据') {
                        $all_td_data[] = [
                            '院校名称' => $college_name,
                            '科类' => $subject,
                            '批次' => $batch,
                            '投档线信息' => $toudang_info
                        ];
                    }
                    break; // 每个院校只处理一次
                }
            }
        }

        $debug_info[] = "投档线信息查询成功，获取到 " . count($all_td_data) . " 条记录";

        // 5. 获取所有招生计划信息（使用zysx_x_functions.php中的函数）
        $all_zsjh_data = [];
        
        foreach ($college_names as $college_name) {
            foreach ($all_sc_data as $sc_item) {
                if ($sc_item['院校名称'] === $college_name) {
                    $subject = $sc_item['科类'];
                    $batch = $sc_item['批次'];
                    
                    // 使用zysx_x_functions.php中的函数获取招生计划信息
                    $zsjh_info = getZsjhInfoMultiYears($conn, $college_name, $subject, $batch);
                    if ($zsjh_info && $zsjh_info !== '该院校在该科类、批次下无招生计划数据') {
                        $all_zsjh_data[] = [
                            '院校名称' => $college_name,
                            '科类' => $subject,
                            '批次' => $batch,
                            '招生计划信息' => $zsjh_info
                        ];
                    }
                    break; // 每个院校只处理一次
                }
            }
        }

        $debug_info[] = "招生计划信息查询成功，获取到 " . count($all_zsjh_data) . " 条记录";

        // 合并数据
        $merged_data = [];
        
        $debug_info[] = "开始合并数据，收藏数据: " . count($all_sc_data) . " 条";
        $debug_info[] = "院校信息: " . count($all_yx_data) . " 条";
        $debug_info[] = "排名信息: " . count($all_rk_data) . " 条";
        $debug_info[] = "投档线信息: " . count($all_td_data) . " 条";
        $debug_info[] = "招生计划信息: " . count($all_zsjh_data) . " 条";
        
        // 创建索引以提高查找效率
        $yx_index = [];
        foreach ($all_yx_data as $yx_item) {
            $yx_index[$yx_item['院校名称']] = $yx_item;
        }
        
        $rk_index = [];
        foreach ($all_rk_data as $rk_item) {
            $rk_index[$rk_item['院校名称']][] = $rk_item;
        }
        
        $td_index = [];
        foreach ($all_td_data as $td_item) {
            $key = $td_item['院校名称'] . '|' . $td_item['科类'] . '|' . $td_item['批次'];
            $td_index[$key] = $td_item['投档线信息'];
        }
        
        $zsjh_index = [];
        foreach ($all_zsjh_data as $zsjh_item) {
            $key = $zsjh_item['院校名称'] . '|' . $zsjh_item['科类'] . '|' . $zsjh_item['批次'];
            $zsjh_index[$key] = $zsjh_item['招生计划信息'];
        }
        
        $debug_info[] = "索引创建完成，开始处理每条记录";
        
        foreach ($all_sc_data as $index => $sc_item) {
            $college_name = $sc_item['院校名称'];
            $subject = $sc_item['科类'];
            $batch = $sc_item['批次'];
            
            // 使用索引快速查找数据
            $college_info = $yx_index[$college_name] ?? null;
            $ranking_info = $rk_index[$college_name] ?? [];
            $toudang_info = $td_index[$college_name . '|' . $subject . '|' . $batch] ?? '该院校在该科类、批次下无投档线数据';
            $zsjh_info = $zsjh_index[$college_name . '|' . $subject . '|' . $batch] ?? '该院校在该科类、批次下无招生计划数据';
            
            // 构建院校信息字符串
            $college_info_str = buildCollegeInfoFromData($college_info);
            
            // 构建排名信息字符串
            $ranking_info_str = buildRankingInfoString($ranking_info);
            
            $merged_data[] = array_merge($sc_item, [
                '院校信息' => $college_info_str,
                '排名信息' => $ranking_info_str,
                '投档线信息' => $toudang_info,
                '招生计划信息' => $zsjh_info
            ]);
            
            if (($index + 1) % 10 == 0) {
                $debug_info[] = "已处理 " . ($index + 1) . " 条记录";
            }
        }
        
        $debug_info[] = "数据合并完成，共 " . count($merged_data) . " 条记录";

        // 按照新的排序规则重新排序：同科类批次按照分数降序，分数相同按排名升序
        $debug_info[] = "开始数据排序";
        usort($merged_data, 'sortByScoreAndRank');
        $debug_info[] = "数据排序完成";

        // 将debug_info添加到全局变量中，以便调用者可以访问
        global $global_debug_info;
        $global_debug_info = $debug_info;

        return $merged_data;
    } catch (Exception $e) {
        // 记录错误到debug_info
        $debug_info[] = "函数执行出错: " . $e->getMessage();
        global $global_debug_info;
        $global_debug_info = $debug_info;
        throw $e;
    }
}

/**
 * 从院校数据构建院校信息字符串
 */
function buildCollegeInfoFromData($college_data)
{
    if (!$college_data) {
        return '院校信息未知';
    }

    $info = [];

    // 省份信息
    if (!empty($college_data['省份名称'])) {
        $info[] = $college_data['省份名称'];
    }

    // 类型信息
    if (!empty($college_data['类型'])) {
        $info[] = $college_data['类型'];
    }

    // 层次信息
    if (!empty($college_data['层次'])) {
        $info[] = $college_data['层次'];
    }

    // 办学性质
    if (!empty($college_data['办学性质'])) {
        $info[] = $college_data['办学性质'];
    }

    // 院校特征
    $features = [];
    if (isset($college_data['f985']) && intval($college_data['f985']) == 1) {
        $features[] = '985';
    } elseif (isset($college_data['f211']) && intval($college_data['f211']) == 1) {
        $features[] = '211';
    }
    if (!empty($college_data['双一流'])) $features[] = $college_data['双一流'];
    if ($college_data['中央部属'] == '1') $features[] = '部委直属';
    if (!empty($college_data['学院层次'])) {
        if (strpos($college_data['学院层次'], '76001') !== false) $features[] = '国家示范性高职院校';
        if (strpos($college_data['学院层次'], '76002') !== false) $features[] = '国家骨干高职院校';
    }
    if (isset($college_data['双高计划']) && $college_data['双高计划'] !== '' && $college_data['双高计划'] !== '0') $features[] = '双高计划';

    if (!empty($features)) {
        $info[] = implode(' ', $features);
    }

    return implode(' | ', $info);
}

/**
 * 从排名数据构建排名信息字符串
 */
function buildRankingInfoString($ranking_data)
{
    if (empty($ranking_data)) {
        return '';
    }

    $rankings = [];
    foreach ($ranking_data as $item) {
        if (!empty($item['排名']) && !empty($item['排名类型'])) {
            $ranking = $item['排名类型'];
            if (!empty($item['分类'])) {
                $ranking .= '(' . $item['分类'] . ')';
            }
            $ranking .= ':' . $item['排名'];
            $rankings[] = $ranking;
        }
    }

    return implode('<br>', $rankings);
}

/**
 * 从投档线数据构建投档线信息字符串
 */
function buildToudangInfoString($toudang_data)
{
    if (empty($toudang_data)) {
        return '该院校在该科类、批次下无投档线数据';
    }

    // 按专业组分组（假设数据包含年份信息，如果没有则按专业组分组）
    $grouped_data = [];
    foreach ($toudang_data as $item) {
        $key = $item['专业组名称'] ?? '未知专业组';
        $grouped_data[$key][] = $item;
    }

    $info = [];
    foreach ($grouped_data as $group_name => $items) {
        $scores = array_column($items, '投档线');
        $ranks = array_column($items, '最低投档排名');

        $maxScore = max($scores);
        $minScore = min($scores);
        $avgScore = round(array_sum($scores) / count($scores), 1);

        $validRanks = array_filter($ranks, function ($rank) {
            return !empty($rank) && is_numeric($rank) && $rank > 0;
        });
        $maxRank = !empty($validRanks) ? max($validRanks) : 0;
        $minRank = !empty($validRanks) ? min($validRanks) : 0;
        $avgRank = !empty($validRanks) ? round(array_sum($validRanks) / count($validRanks), 0) : 0;

        $groupInfo = "{$group_name}: 最高{$maxScore}分 最低{$minScore}分 平均{$avgScore}分";

        if ($maxRank > 0) {
            $groupInfo .= " 排名{$minRank}-{$maxRank} 平均{$avgRank}名";
        }

        $info[] = $groupInfo;
    }

    return implode("\n", $info);
}

/**
 * 从招生计划数据构建招生计划信息字符串
 */
function buildZsjhInfoString($zsjh_data)
{
    if (empty($zsjh_data)) {
        return '该院校在该科类、批次下无招生计划数据';
    }

    // 按专业组分组
    $grouped_data = [];
    foreach ($zsjh_data as $item) {
        $key = $item['专业组名称'] ?? '未知专业组';
        $grouped_data[$key][] = $item;
    }

    $info = [];
    foreach ($grouped_data as $group_name => $items) {
        $sumPlan = array_sum(array_column($items, '计划数'));
        $info[] = "{$group_name}: 计划数 {$sumPlan}";
    }

    return implode("\n", $info);
}

/**
 * 排序函数：同科类批次按照分数降序，分数相同按排名升序
 */
function sortByScoreAndRank($a, $b)
{
    // 科类排序
    $subjectOrder = ['物理类' => 1, '历史类' => 2];
    $subjectA = $subjectOrder[$a['科类']] ?? 3;
    $subjectB = $subjectOrder[$b['科类']] ?? 3;

    if ($subjectA != $subjectB) {
        return $subjectA - $subjectB;
    }

    // 批次排序
    $batchOrder = ['提前本科' => 1, '本科' => 2, '提前专科' => 3, '专科' => 4];
    $batchA = $batchOrder[$a['批次']] ?? 5;
    $batchB = $batchOrder[$b['批次']] ?? 5;

    if ($batchA != $batchB) {
        return $batchA - $batchB;
    }

    // 提取投档线信息中的平均分数进行排序
    $scoreA = extractAverageScore($a['投档线信息']);
    $scoreB = extractAverageScore($b['投档线信息']);

    // 分数降序排序
    if ($scoreA != $scoreB) {
        return $scoreB - $scoreA;
    }

    // 分数相同时，按排名升序排序
    $rankA = extractAverageRank($a['投档线信息']);
    $rankB = extractAverageRank($b['投档线信息']);

    if ($rankA != $rankB) {
        return $rankA - $rankB;
    }

    // 如果分数和排名都相同，按院校名称排序
    return strcmp($a['院校名称'], $b['院校名称']);
}

/**
 * 从投档线信息中提取平均分数
 */
function extractAverageScore($toudangInfo)
{
    if (empty($toudangInfo)) return 0;

    // 匹配所有年份的平均分数，取最高年份的平均分
    if (preg_match_all('/\d{4}年: .*? 平均(\d+(?:\.\d+)?)分/', $toudangInfo, $matches)) {
        $scores = array_map('floatval', $matches[1]);
        return max($scores); // 返回最高年份的平均分
    }

    return 0;
}

/**
 * 从投档线信息中提取平均排名
 */
function extractAverageRank($toudangInfo)
{
    if (empty($toudangInfo)) return 999999;

    // 匹配所有年份的平均排名，取最高年份的平均排名
    if (preg_match_all('/\d{4}年: .*? 平均(\d+)/', $toudangInfo, $matches)) {
        $ranks = array_map('intval', $matches[1]);
        return min($ranks); // 返回最高年份的平均排名（排名越小越好）
    }

    return 999999; // 没有排名时返回很大的数字
}

/**
 * 清理数据中的特殊字符，确保Excel能正确解析
 */
function cleanDataForExcel($data) {
    if (is_array($data)) {
        return array_map('cleanDataForExcel', $data);
    }
    
    if (is_string($data)) {
        // 移除可能导致Excel解析错误的字符
        $data = str_replace(["\r\n", "\r", "\n"], "\r\n", $data); // 统一换行符
        $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data); // 移除控制字符
        $data = trim($data); // 移除首尾空白
    }
    
    return $data;
}

/**
 * 导出到Excel功能（与预览表格内容一致）
 */
function exportToExcel()
{
    global $conn;

    try {
        // 检查用户登录状态
        if (!isset($_COOKIE['loginid']) || empty($_COOKIE['loginid'])) {
            echo '<script>alert("用户未登录，无法导出数据"); window.close();</script>';
            exit;
        }

        // 由于ZipStream库存在兼容性问题，使用HTML表格格式导出Excel
        // 这种方式生成的文件可以被Excel正确打开，并且避免了ZipStream的问题
        exportToExcelHTML();
        return;

        // 设置用户ID
        $dlid = $_COOKIE['loginid'];

        // 使用与AJAX预览相同的数据获取逻辑
        $previewData = getExportData($conn, $dlid);

        // 清理数据，确保Excel能正确解析
        $previewData = cleanDataForExcel($previewData);

        if (empty($previewData)) {
            // 如果没有数据，返回提示
            echo '<script>alert("暂无收藏数据可导出"); window.close();</script>';
            exit;
        }

        // 设置响应头
        $filename = "志愿表_" . date('Y-m-d_H-i-s') . ".xlsx";
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // 创建Excel文件
        require_once 'vendor/autoload.php';

        // 强制PhpSpreadsheet使用ZipArchive而不是ZipStream
        if (class_exists('ZipArchive')) {
            // 通过设置环境变量强制使用ZipArchive
            putenv('PHPSPREADSHEET_ZIPARCHIVE=1');
        }

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表格标题
        $sheet->setCellValueExplicit('A1', date('Y') . '年高考志愿表', \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
        $sheet->mergeCells('A1:L1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getRowDimension(1)->setRowHeight(30);

        // 设置用户信息（从数据库获取）
        $userInfo = '';
        try {
            $sql_user = "SELECT bkkl, xk, gkfs, spm FROM user WHERE id = ?";
            $stmt_user = $conn->prepare($sql_user);
            if ($stmt_user) {
                $stmt_user->bind_param("i", $dlid);
                if ($stmt_user->execute()) {
                    $result_user = $stmt_user->get_result();
                    $user_row = $result_user->fetch_assoc();
                    if ($user_row && !empty($user_row['bkkl'])) {
                        $userInfo = "（科类：{$user_row['bkkl']}、选科：{$user_row['xk']}、分数：{$user_row['gkfs']}、排名：{$user_row['spm']}）";
                    }
                }
                $stmt_user->close();
            }
        } catch (Exception $e) {
            // 用户信息获取失败时不影响导出
        }
        $sheet->setCellValueExplicit('A2', $userInfo, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
        $sheet->mergeCells('A2:L2');
        $sheet->getStyle('A2')->getFont()->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getRowDimension(2)->setRowHeight(20);

        // 设置说明信息
        $sheet->setCellValueExplicit('A3', '注：投档信息中平均分和排名是指所有专业组平均投档分和投档排名', \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
        $sheet->mergeCells('A3:L3');
        $sheet->getStyle('A3')->getFont()->setSize(10)->setItalic(true);
        $sheet->getStyle('A3')->getFont()->getColor()->setRGB('FF0000');
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getRowDimension(3)->setRowHeight(18);

        // 设置标题行（按照预览表格的顺序）
        $headers = [
            '序号', '科类', '批次', '院校名称', '院校信息', '排名信息', 
            '投档线信息', '招生计划信息', '专业组名称', '专业名称', 
            '计划数', '选科要求'
        ];

        // 设置标题行样式（第4行）
        foreach ($headers as $colIndex => $header) {
            $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
            $sheet->setCellValueExplicit($colLetter . '4', $header, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
            
            // 设置标题样式
            $titleStyle = $sheet->getStyle($colLetter . '4');
            $titleStyle->getFont()->setBold(true)->setSize(12);
            $titleStyle->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('4A90E2');
            $titleStyle->getFont()->getColor()->setRGB('FFFFFF');
            $titleStyle->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER)
                ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
        }

        // 简化的数据填充方式 - 逐行写入，避免复杂操作
        $rowIndex = 5;
        $processedColleges = [];
        $processedGroups = [];
        $currentRowNumber = 0;

        foreach ($previewData as $index => $item) {
            $collegeKey = $item['科类'] . '|' . $item['批次'] . '|' . $item['院校名称'];
            $groupKey = $collegeKey . '|' . $item['专业组名称'];

            $isNewCollege = !in_array($collegeKey, $processedColleges);
            $isNewGroup = !in_array($groupKey, $processedGroups);

            if ($isNewCollege) {
                $processedColleges[] = $collegeKey;
                $currentRowNumber++;
            }

            if ($isNewGroup) {
                $processedGroups[] = $groupKey;
            }

            // 简化数据写入 - 直接使用setCellValue
            $sheet->setCellValue('A' . $rowIndex, $isNewCollege ? $currentRowNumber : '');
            $sheet->setCellValue('B' . $rowIndex, $isNewCollege ? $item['科类'] : '');
            $sheet->setCellValue('C' . $rowIndex, $isNewCollege ? $item['批次'] : '');
            $sheet->setCellValue('D' . $rowIndex, $isNewCollege ? $item['院校名称'] : '');
            $sheet->setCellValue('E' . $rowIndex, $isNewCollege ? $item['院校信息'] : '');
            $sheet->setCellValue('F' . $rowIndex, $isNewCollege ? str_replace('<br>', "\n", $item['排名信息']) : '');
            $sheet->setCellValue('G' . $rowIndex, $isNewCollege ? str_replace("\n", "\n", $item['投档线信息']) : '');
            $sheet->setCellValue('H' . $rowIndex, $isNewCollege ? str_replace("\n", "\n", $item['招生计划信息']) : '');
            $sheet->setCellValue('I' . $rowIndex, $isNewGroup ? $item['专业组名称'] : '');
            $sheet->setCellValue('J' . $rowIndex, $item['专业名称']);
            $sheet->setCellValue('K' . $rowIndex, $item['计划数']);
            $sheet->setCellValue('L' . $rowIndex, $item['选科要求']);

            $rowIndex++;
        }

        // 设置列宽（根据内容调整）
        $columnWidths = [
            'A' => 8,   // 序号
            'B' => 10,  // 科类
            'C' => 12,  // 批次
            'D' => 25,  // 院校名称
            'E' => 40,  // 院校信息
            'F' => 30,  // 排名信息
            'G' => 35,  // 投档线信息
            'H' => 25,  // 招生计划信息
            'I' => 20,  // 专业组名称
            'J' => 25,  // 专业名称
            'K' => 10,  // 计划数
            'L' => 15   // 选科要求
        ];
        
        foreach ($columnWidths as $colLetter => $width) {
            $sheet->getColumnDimension($colLetter)->setWidth($width);
        }

        // 设置表格边框和样式
        $lastRow = $rowIndex - 1;
        $lastCol = count($headers);
        $lastColLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($lastCol);
        
        // 设置数据区域的边框（从第4行开始）
        $sheet->getStyle("A4:" . $lastColLetter . $lastRow)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        
        // 设置数据区域样式
        $dataStyle = $sheet->getStyle("A5:" . $lastColLetter . $lastRow);
        $dataStyle->getAlignment()
            ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT)
            ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_TOP)
            ->setWrapText(true);
        
        // 设置交替行颜色（从第5行开始）
        for ($i = 5; $i <= $lastRow; $i++) {
            if (($i - 4) % 2 == 0) {
                $sheet->getStyle("A$i:" . $lastColLetter . $i)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('F8F9FA');
            }
            
            // 设置行高
            $sheet->getRowDimension($i)->setRowHeight(60); // 固定行高以适应多行文本
        }

        // 设置标题行高度
        $sheet->getRowDimension(4)->setRowHeight(25);

        // 创建Excel写入器
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

        // 设置写入器选项
        $writer->setPreCalculateFormulas(false);
        $writer->setIncludeCharts(false);

        // 尝试强制使用ZipArchive而不是ZipStream
        try {
            // 使用反射来设置内部的zip类
            $reflection = new \ReflectionClass($writer);
            if ($reflection->hasProperty('zip')) {
                $zipProperty = $reflection->getProperty('zip');
                $zipProperty->setAccessible(true);
                // 如果可能的话，设置为使用ZipArchive
            }
        } catch (Exception $e) {
            // 如果反射失败，继续使用默认设置
        }

        // 先保存到临时文件，然后输出，避免ZipStream的gzdeflate问题
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_export_') . '.xlsx';

        try {
            $writer->save($tempFile);

            // 检查文件是否成功创建
            if (!file_exists($tempFile) || filesize($tempFile) == 0) {
                throw new Exception("Excel文件生成失败");
            }

            // 输出文件内容
            readfile($tempFile);

            // 清理临时文件
            unlink($tempFile);

        } catch (Exception $e) {
            // 清理临时文件
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
            throw $e;
        }

        exit;
    } catch (Exception $e) {
        // 确保在出错时不输出Excel头信息
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=utf-8');
        }
        echo '<script>alert("导出失败：' . addslashes($e->getMessage()) . '"); history.back();</script>';
        exit;
    }
}

/**
 * HTML格式Excel导出功能（避免ZipStream问题的解决方案）
 */
function exportToExcelHTML()
{
    global $conn;

    try {
        // 检查用户登录状态
        if (!isset($_COOKIE['loginid']) || empty($_COOKIE['loginid'])) {
            echo '<script>alert("用户未登录，无法导出数据"); window.close();</script>';
            exit;
        }

        // 设置用户ID
        $dlid = $_COOKIE['loginid'];

        // 使用与AJAX预览相同的数据获取逻辑
        $previewData = getExportData($conn, $dlid);

        if (empty($previewData)) {
            // 如果没有数据，返回提示
            echo '<script>alert("暂无收藏数据可导出"); window.close();</script>';
            exit;
        }

        // 设置Excel响应头（HTML格式但Excel可以打开）
        $filename = "志愿表_" . date('Y-m-d_H-i-s') . ".xls";
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // 输出UTF-8 BOM
        echo "\xEF\xBB\xBF";

        // 开始HTML表格
        echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
        echo '<head>';
        echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8">';
        echo '<meta name="ProgId" content="Excel.Sheet">';
        echo '<meta name="Generator" content="Microsoft Excel 11">';
        echo '<style>';
        // ========== 整体字体大小调整区域 ==========
        // 主要字体大小：13px（原来是11px）- 这里调整整体表格字体大小
        echo 'table { border-collapse: collapse; width: 100%; font-size: 13px; font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif; }';
        echo 'th, td { border: 0.5pt solid #333; padding: 4px 6px; text-align: left; vertical-align: middle; }';
        // 表头字体大小：12px（原来是10px）- 这里调整表头字体大小
        echo 'th { background-color: #E8F4FD; color: #2C5282; font-weight: bold; text-align: center; font-size: 12px; height: 25px; }';
        echo 'td { background-color: #FFFFFF; }';
        echo 'tr:nth-child(even) td { background-color: #F8FAFE; }';
        echo '.title { font-size: 18px; font-weight: bold; text-align: center; border: none; padding: 10px; background-color: #FFFFFF; color: #1A365D; }';
        echo '.subtitle { font-size: 13px; text-align: center; border: none; padding: 5px; background-color: #FFFFFF; color: #4A5568; }';
        echo '.note { font-size: 13px; color: #E53E3E; text-align: center; font-style: italic; border: none; padding: 3px; background-color: #FFFFFF; }';
        echo '.number-cell { text-align: center; font-weight: bold; background-color: #EDF2F7; }';
        echo '.college-name { font-weight: bold; color: #2D3748; }';
        echo '.major-name { color: #4A5568; }';
        echo '.plan-count { text-align: center; font-weight: bold; color: #2B6CB0; }';
        // 选科要求字体大小：11px（原来是10px）
        echo '.subject-req { font-size: 13px; color: #718096; }';
        echo '</style>';
        echo '</head>';
        echo '<body>';

        // 表格标题
        echo '<table>';
        echo '<tr><td colspan="12" class="title">' . date('Y') . '年高考志愿表</td></tr>';

        // 用户信息
        $userInfo = '';
        try {
            $sql_user = "SELECT bkkl, xk, gkfs, spm FROM user WHERE id = ?";
            $stmt_user = $conn->prepare($sql_user);
            if ($stmt_user) {
                $stmt_user->bind_param("i", $dlid);
                if ($stmt_user->execute()) {
                    $result_user = $stmt_user->get_result();
                    $user_row = $result_user->fetch_assoc();
                    if ($user_row && !empty($user_row['bkkl'])) {
                        $userInfo = "🎓 科类：{$user_row['bkkl']} | 📝 选科：{$user_row['xk']} | 📊 分数：{$user_row['gkfs']} | 📈 排名：{$user_row['spm']}";
                    }
                }
                $stmt_user->close();
            }
        } catch (Exception $e) {
            // 用户信息获取失败时不影响导出
        }
        echo '<tr><td colspan="12" class="subtitle">' . htmlspecialchars($userInfo) . '</td></tr>';

        // 说明信息
        echo '<tr><td colspan="12" class="note">💡 注：投档信息中平均分和排名是指所有专业组平均投档分和投档排名</td></tr>';

        // 空行分隔
        echo '<tr><td colspan="12" style="height: 5px; border: none; background-color: #FFFFFF;"></td></tr>';

        // 表头
        echo '<tr>';
        $headers = [
            '序号', '科类', '批次', '院校名称', '院校信息', '排名信息',
            '投档线信息', '招生计划信息', '专业组名称', '专业名称',
            '计划数', '选科要求'
        ];
        foreach ($headers as $index => $header) {
            // ========== 列宽调整区域 ==========
            $width = '';
            switch ($index) {
                case 0: $width = 'width: 40px;'; break;  // 序号
                case 1: $width = 'width: 60px;'; break;  // 科类
                case 2: $width = 'width: 60px;'; break;  // 批次
                case 3: $width = 'width: 120px;'; break; // 院校名称
                case 4: $width = 'width: 180px;'; break; // 院校信息
                case 5: $width = 'width: 100px;'; break;  // 排名信息
                // *** G列宽度调整 *** - 投档线信息列宽度：160px（原来是120px）
                case 6: $width = 'width: 300px;'; break; // 投档线信息 (G列加宽)
                case 7: $width = 'width: 160px;'; break; // 招生计划信息 (H列加宽)
                case 8: $width = 'width: 80px;'; break;  // 专业组名称 (I列缩小)
                case 9: $width = 'width: 200px;'; break; // 专业名称
                case 10: $width = 'width: 50px;'; break; // 计划数
                case 11: $width = 'width: 80px;'; break; // 选科要求
            }
            echo '<th style="' . $width . '">' . htmlspecialchars($header) . '</th>';
        }
        echo '</tr>';

        // 数据行 - 优化版本，支持单元格合并
        $processedColleges = [];
        $processedGroups = [];
        $currentRowNumber = 0;

        // 预处理数据，计算每个学校和专业组的行数
        $collegeRowCounts = [];
        $groupRowCounts = [];

        foreach ($previewData as $item) {
            $collegeKey = $item['科类'] . '|' . $item['批次'] . '|' . $item['院校名称'];
            $groupKey = $collegeKey . '|' . $item['专业组名称'];

            if (!isset($collegeRowCounts[$collegeKey])) {
                $collegeRowCounts[$collegeKey] = 0;
            }
            $collegeRowCounts[$collegeKey]++;

            if (!isset($groupRowCounts[$groupKey])) {
                $groupRowCounts[$groupKey] = 0;
            }
            $groupRowCounts[$groupKey]++;
        }

        foreach ($previewData as $index => $item) {
            $collegeKey = $item['科类'] . '|' . $item['批次'] . '|' . $item['院校名称'];
            $groupKey = $collegeKey . '|' . $item['专业组名称'];

            $isNewCollege = !in_array($collegeKey, $processedColleges);
            $isNewGroup = !in_array($groupKey, $processedGroups);

            if ($isNewCollege) {
                $processedColleges[] = $collegeKey;
                $currentRowNumber++;
            }

            if ($isNewGroup) {
                $processedGroups[] = $groupKey;
            }

            echo '<tr>';

            // 序号 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                echo '<td class="number-cell" rowspan="' . $rowspan . '">' . $currentRowNumber . '</td>';
            }

            // 科类 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                echo '<td rowspan="' . $rowspan . '">' . htmlspecialchars($item['科类']) . '</td>';
            }

            // 批次 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                echo '<td rowspan="' . $rowspan . '">' . htmlspecialchars($item['批次']) . '</td>';
            }

            // 院校名称 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                echo '<td class="college-name" rowspan="' . $rowspan . '">' . htmlspecialchars($item['院校名称']) . '</td>';
            }

            // 院校信息 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                echo '<td rowspan="' . $rowspan . '">' . htmlspecialchars($item['院校信息']) . '</td>';
            }

            // 排名信息 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                $rankingInfo = str_replace(['<br>', '<br/>'], '<br/>', htmlspecialchars($item['排名信息']));
                echo '<td rowspan="' . $rowspan . '">' . $rankingInfo . '</td>';
            }

            // 投档线信息 - 合并单元格（G列内容）
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                $scoreInfo = nl2br(htmlspecialchars($item['投档线信息']));
                // G列内容字体大小：11px - 这里调整投档线信息的字体大小
                echo '<td rowspan="' . $rowspan . '">' . $scoreInfo . '</td>';
            }

            // 招生计划信息 - 合并单元格
            if ($isNewCollege) {
                $rowspan = $collegeRowCounts[$collegeKey];
                $planInfo = nl2br(htmlspecialchars($item['招生计划信息']));
                echo '<td rowspan="' . $rowspan . '">' . $planInfo . '</td>';
            }

            // 专业组名称 - 合并单元格
            if ($isNewGroup) {
                $rowspan = $groupRowCounts[$groupKey];
                echo '<td rowspan="' . $rowspan . '" style="font-weight: 500;">' . htmlspecialchars($item['专业组名称']) . '</td>';
            }

            // 专业名称 - 不合并
            echo '<td class="major-name">' . htmlspecialchars($item['专业名称']) . '</td>';

            // 计划数 - 不合并
            echo '<td class="plan-count">' . htmlspecialchars($item['计划数']) . '</td>';

            // 选科要求 - 不合并
            echo '<td class="subject-req">' . htmlspecialchars($item['选科要求']) . '</td>';

            echo '</tr>';
        }

        echo '</table>';
        echo '</body>';
        echo '</html>';

        exit;

    } catch (Exception $e) {
        // 确保在出错时不输出Excel头信息
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=utf-8');
        }
        echo '<script>alert("导出失败：' . addslashes($e->getMessage()) . '"); history.back();</script>';
        exit;
    }
}



/**
 * 获取导出数据（与AJAX预览使用相同的逻辑）
 */
function getExportData($conn, $user_id) {
    try {
        // 获取基本的收藏数据
        $sql_data = "SELECT
                        ws.科类,
                        ws.批次,
                        ws.院校名称,
                        ws.专业组名称,
                        ws.专业名称,
                        ws.计划数,
                        ws.选科要求,
                        ws.收藏年份,
                        ws.收藏时间
                    FROM word_sc ws
                    WHERE ws.用户ID = ?
                    ORDER BY
                        CASE ws.科类
                            WHEN '物理类' THEN 1
                            WHEN '历史类' THEN 2
                            ELSE 3
                        END,
                        CASE ws.批次
                            WHEN '提前本科' THEN 1
                            WHEN '本科' THEN 2
                            WHEN '提前专科' THEN 3
                            WHEN '专科' THEN 4
                            ELSE 5
                        END,
                        ws.专业组名称 ASC,
                        ws.院校名称 ASC,
                        ws.专业名称 ASC";

        $stmt_data = $conn->prepare($sql_data);
        if (!$stmt_data) {
            throw new Exception("数据查询预处理失败: " . $conn->error);
        }

        $stmt_data->bind_param("i", $user_id);
        if (!$stmt_data->execute()) {
            throw new Exception("数据查询执行失败: " . $stmt_data->error);
        }

        $result_data = $stmt_data->get_result();
        $all_data = [];

        while ($row = $result_data->fetch_assoc()) {
            $college_name = $row['院校名称'];

            // 为每条记录添加基本信息
            $item = array_merge($row, [
                '院校信息' => '院校信息加载中...',
                '排名信息' => '排名信息加载中...',
                '投档线信息' => '投档线信息加载中...',
                '招生计划信息' => '招生计划信息加载中...'
            ]);

            // 获取院校基本信息
            $sql_yx = "SELECT 省份名称, 类型, 层次, 办学性质, f211, f985, 双一流, 中央部属 FROM yxinfo WHERE 院校名称 = ? LIMIT 1";
            $stmt_yx = $conn->prepare($sql_yx);
            if ($stmt_yx) {
                $stmt_yx->bind_param("s", $college_name);
                if ($stmt_yx->execute()) {
                    $result_yx = $stmt_yx->get_result();
                    $yx_row = $result_yx->fetch_assoc();
                    if ($yx_row) {
                        $info_parts = [];
                        if (!empty($yx_row['省份名称'])) $info_parts[] = $yx_row['省份名称'];
                        if (!empty($yx_row['类型'])) $info_parts[] = $yx_row['类型'];
                        if (!empty($yx_row['层次'])) $info_parts[] = $yx_row['层次'];
                        if (!empty($yx_row['办学性质'])) $info_parts[] = $yx_row['办学性质'];

                        // 院校特征
                        $features = [];
                        if (isset($yx_row['f985']) && intval($yx_row['f985']) == 1) $features[] = '985';
                        elseif (isset($yx_row['f211']) && intval($yx_row['f211']) == 1) $features[] = '211';
                        if (!empty($yx_row['双一流'])) $features[] = $yx_row['双一流'];
                        if ($yx_row['中央部属'] == '1') $features[] = '部委直属';

                        if (!empty($features)) {
                            $info_parts[] = implode(' ', $features);
                        }

                        $item['院校信息'] = implode(' | ', $info_parts);
                    }
                }
                $stmt_yx->close();
            }

            // 获取排名信息
            $sql_rk = "SELECT 排名类型, 排名, 分类 FROM pm_rk WHERE 院校名称_rkpm = ? ORDER BY 排名 ASC LIMIT 5";
            $stmt_rk = $conn->prepare($sql_rk);
            if ($stmt_rk) {
                $stmt_rk->bind_param("s", $college_name);
                if ($stmt_rk->execute()) {
                    $result_rk = $stmt_rk->get_result();
                    $rankings = [];
                    while ($rk_row = $result_rk->fetch_assoc()) {
                        if (!empty($rk_row['排名']) && !empty($rk_row['排名类型'])) {
                            $ranking = $rk_row['排名类型'];
                            if (!empty($rk_row['分类'])) {
                                $ranking .= '(' . $rk_row['分类'] . ')';
                            }
                            $ranking .= ':' . $rk_row['排名'];
                            $rankings[] = $ranking;
                        }
                    }

                    if (!empty($rankings)) {
                        $item['排名信息'] = implode('<br>', $rankings);
                    } else {
                        $item['排名信息'] = '暂无排名信息';
                    }
                } else {
                    $item['排名信息'] = '排名信息查询失败';
                }
                $stmt_rk->close();
            } else {
                $item['排名信息'] = '排名信息查询预处理失败';
            }

            // 获取投档线信息
            $item['投档线信息'] = getToudangInfoMultiYears($conn, $college_name, $row['科类'], $row['批次']);

            // 获取招生计划信息
            $item['招生计划信息'] = getZsjhInfoMultiYears($conn, $college_name, $row['科类'], $row['批次']);

            $all_data[] = $item;
        }

        return $all_data;

    } catch (Exception $e) {
        throw new Exception("获取导出数据失败: " . $e->getMessage());
    }
}
?>
<?php //include("dhtool.php");
?>

<script>
    // 专业信息和志愿预览显示/隐藏功能
    document.addEventListener('DOMContentLoaded', function() {
        const toggleMajorButton = document.getElementById('toggleMajorInfo');
        const togglePreviewButton = document.getElementById('togglePreviewTable');
        let majorColumns = []; // 改为数组类型
        const previewContainer = document.getElementById('previewTableContainer');
        const previewTableBody = document.getElementById('previewTableBody');

        let isMajorInfoVisible = false; // 默认隐藏专业信息
        let isPreviewTableVisible = false; // 默认隐藏志愿预览表格
        let previewData = null; // 存储预览数据

        // 页面加载时隐藏专业信息列和预览表格，隐藏显示专业信息按钮
        function hideMajorColumns() {
            majorColumns.forEach(function(column) {
                column.style.display = 'none';
            });
        }

        function showMajorColumns() {
            majorColumns.forEach(function(column) {
                column.style.display = '';
            });
        }

        // 初始化时隐藏专业信息列
        hideMajorColumns();
        toggleMajorButton.style.display = 'none'; // 默认隐藏显示专业信息按钮
        
        // 初始化加载状态
        let isLoading = false;
        let loadingStartTime = null;

        // 显示专业信息按钮点击事件
        toggleMajorButton.addEventListener('click', function() {
            if (isMajorInfoVisible) {
                // 隐藏专业信息
                hideMajorColumns();
                toggleMajorButton.innerHTML = '<i class="layui-icon layui-icon-eye"></i> 显示专业信息';
                isMajorInfoVisible = false;
            } else {
                // 显示专业信息
                showMajorColumns();
                toggleMajorButton.innerHTML = '<i class="layui-icon layui-icon-eye-invisible"></i> 隐藏专业信息';
                isMajorInfoVisible = true;
            }
        });

        // 志愿预览按钮点击事件 - 使用事件委托
        document.addEventListener('click', function(event) {
            // 检查点击的是否是预览按钮
            if (event.target.closest('#togglePreviewTable')) {
                // 检查按钮当前状态，如果包含"重新加载"则重新加载数据
                if (togglePreviewButton.innerHTML.includes('重新加载')) {
                    // 点击重新加载，先清理原有表格数据
                    clearPreviewTable();
                    // 然后重新获取数据
                    loadPreviewData();
                    return; // 直接返回，不执行后续逻辑
                }

                if (isPreviewTableVisible) {
                    // 隐藏志愿预览表格
                    previewContainer.style.display = 'none';
                    togglePreviewButton.innerHTML = '<i class="layui-icon layui-icon-table"></i> 志愿预览';

                    // 隐藏显示专业信息按钮
                    toggleMajorButton.style.display = 'none';

                    // 同时隐藏专业信息
                    hideMajorColumns();
                    toggleMajorButton.innerHTML = '<i class="layui-icon layui-icon-eye"></i> 显示专业信息';
                    isMajorInfoVisible = false;
                    isPreviewTableVisible = false;
                } else {
                    // 显示志愿预览表格或重新加载数据
                    if (!previewData) {
                        // 第一次点击，需要加载数据
                        loadPreviewData();
                    } else {
                        // 数据已加载，直接显示表格
                        previewContainer.style.display = 'block';
                        togglePreviewButton.innerHTML = '<i class="layui-icon layui-icon-refresh"></i> 重新加载';
                        toggleMajorButton.style.display = 'inline-block'; // 显示显示专业信息按钮
                        isPreviewTableVisible = true;
                    }
                }
            }
        });

        // 清理预览表格数据的函数
        function clearPreviewTable() {
            // 清空表格内容
            previewTableBody.innerHTML = '<tr><td colspan="12" style="text-align: center; color: #999;">正在重新加载数据...</td></tr>';
            
            // 清空预览数据
            previewData = null;
            
            // 重置状态
            isPreviewTableVisible = false;
            
            // 隐藏专业信息按钮
            toggleMajorButton.style.display = 'none';
            
            // 隐藏专业信息列
            hideMajorColumns();
            toggleMajorButton.innerHTML = '<i class="layui-icon layui-icon-eye"></i> 显示专业信息';
            isMajorInfoVisible = false;
        }

        // 加载预览数据的函数
        function loadPreviewData() {
            // 设置加载状态
            isLoading = true;
            loadingStartTime = Date.now();
            togglePreviewButton.disabled = true;
            
            // 开始显示加载百分比
            startLoadingProgress();

            // 发送AJAX请求加载数据
            const formData = new FormData();
            formData.append('load_preview', 'true');

            fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 停止加载进度显示
                        stopLoadingProgress();
                        
                        console.log('数据加载成功:', data.data);
                        
                        // 输出调试信息到控制台
                        if (data.debug && data.debug.length > 0) {
                            console.log('=== 后端调试信息 ===');
                            data.debug.forEach((info, index) => {
                                console.log(`[${index + 1}] ${info}`);
                            });
                            console.log('=== 调试信息结束 ===');
                        }
                        
                        previewData = data.data;
                        renderPreviewTable(data.data);

                        // 显示表格和按钮
                        previewContainer.style.display = 'block';
                        togglePreviewButton.innerHTML = '<i class="layui-icon layui-icon-refresh"></i> 重新加载';
                        togglePreviewButton.disabled = false; // 重新启用按钮
                        toggleMajorButton.style.display = 'inline-block'; // 显示显示专业信息按钮
                        isPreviewTableVisible = true;
                    } else {
                        console.error('加载数据失败:', data.msg);
                        
                        // 输出调试信息到控制台
                        if (data.debug && data.debug.length > 0) {
                            console.log('=== 后端调试信息 ===');
                            data.debug.forEach((info, index) => {
                                console.log(`[${index + 1}] ${info}`);
                            });
                            console.log('=== 调试信息结束 ===');
                        }
                        
                        // 使用layui的layer组件显示错误信息
                        if (typeof layui !== 'undefined' && layui.layer) {
                            layui.layer.msg(data.msg || '加载数据失败，请重试', {icon: 2, time: 3000});
                        } else {
                            alert('加载数据失败，请重试');
                        }
                        resetPreviewButton();
                    }
                })
                .catch(error => {
                    console.error('网络请求错误:', error);
                    // 使用layui的layer组件显示错误信息
                    if (typeof layui !== 'undefined' && layui.layer) {
                        layui.layer.msg('网络错误，请检查网络连接后重试', {icon: 2, time: 3000});
                    } else {
                        alert('加载数据失败，请重试');
                    }
                    stopLoadingProgress();
                    resetPreviewButton();
                });
        }

        // 开始显示加载进度
        function startLoadingProgress() {
            let progress = 0;
            const progressInterval = setInterval(() => {
                if (!isLoading) {
                    clearInterval(progressInterval);
                    return;
                }
                
                // 模拟进度增长，但不超过90%（留出实际完成的空间）
                if (progress < 90) {
                    progress += Math.random() * 2 + 1; // 随机增加1-3%
                    if (progress > 90) progress = 90;
                }
                
                // 在按钮上显示加载百分比
                togglePreviewButton.innerHTML = `<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中 ${Math.round(progress)}%`;
                
                // 存储定时器ID，以便后续清除
                togglePreviewButton.dataset.progressInterval = progressInterval;
            }, 100);
        }

        // 停止加载进度显示
        function stopLoadingProgress() {
            isLoading = false;
            
            // 清除进度定时器
            if (togglePreviewButton.dataset.progressInterval) {
                clearInterval(parseInt(togglePreviewButton.dataset.progressInterval));
                delete togglePreviewButton.dataset.progressInterval;
            }
            
            // 按钮样式现在由CSS自动处理，无需手动设置
        }

        // 渲染预览表格的函数
        function renderPreviewTable(data) {
            if (!data || data.length === 0) {
                previewTableBody.innerHTML = '<tr><td colspan="12" style="text-align: center; color: #999;">暂无数据</td></tr>';
                return;
            }

            let html = '';
            let processedColleges = [];
            let processedGroups = [];
            let rowIndex = 0;

            data.forEach((item, index) => {
                const collegeKey = item['科类'] + '|' + item['批次'] + '|' + item['院校名称'];
                const groupKey = collegeKey + '|' + item['专业组名称'];

                const isNewCollege = !processedColleges.includes(collegeKey);
                const isNewGroup = !processedGroups.includes(groupKey);

                if (isNewCollege) {
                    processedColleges.push(collegeKey);
                    rowIndex++;
                    const collegeSpan = data.filter(row =>
                        row['科类'] === item['科类'] &&
                        row['批次'] === item['批次'] &&
                        row['院校名称'] === item['院校名称']
                    ).length;

                    html += `<tr class="college-row">`;
                    html += `<td rowspan="${collegeSpan}">${rowIndex}</td>`;
                    html += `<td rowspan="${collegeSpan}">${escapeHtml(item['科类'] || '')}</td>`;
                    html += `<td rowspan="${collegeSpan}">${escapeHtml(item['批次'] || '')}</td>`;
                    html += `<td rowspan="${collegeSpan}">${escapeHtml(item['院校名称'] || '')}</td>`;
                    html += `<td rowspan="${collegeSpan}" class="college-info">${escapeHtml(item['院校信息'] || '')}</td>`;
                    html += `<td rowspan="${collegeSpan}" class="ranking-info">${escapeHtml(item['排名信息'] || '')}</td>`;
                    html += `<td rowspan="${collegeSpan}" class="toudang-info">${escapeHtml(item['投档线信息'] || '')}</td>`;
                    html += `<td rowspan="${collegeSpan}" class="zsjh-info">${escapeHtml(item['招生计划信息'] || '')}</td>`;
                }

                if (isNewGroup) {
                    processedGroups.push(groupKey);
                    const groupSpan = data.filter(row =>
                        row['科类'] === item['科类'] &&
                        row['批次'] === item['批次'] &&
                        row['院校名称'] === item['院校名称'] &&
                        row['专业组名称'] === item['专业组名称']
                    ).length;

                    html += `<td rowspan="${groupSpan}" class="major-info-column">${escapeHtml(item['专业组名称'] || '')}</td>`;
                }

                html += `<td class="major-info-column">${escapeHtml(item['专业名称'] || '')}</td>`;
                html += `<td class="major-info-column">${escapeHtml(item['计划数'] || '未知')}</td>`;
                html += `<td class="major-info-column">${escapeHtml(item['选科要求'] || '')}</td>`;
                html += `</tr>`;
            });

            previewTableBody.innerHTML = html;

            // 重新获取专业信息列选择器，因为动态生成的HTML元素可能不在初始选择器中
            const newMajorColumns = document.querySelectorAll('.major-info-column');

            // 更新全局的majorColumns变量
            majorColumns = Array.from(newMajorColumns);

            // 默认隐藏专业信息列
            hideMajorColumns();
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 重置预览按钮状态
        function resetPreviewButton() {
            togglePreviewButton.innerHTML = '<i class="layui-icon layui-icon-table"></i> 志愿预览';
            togglePreviewButton.disabled = false;
        }

        // ========== 导出Excel提示框功能 ==========

        // Excel导出按钮点击事件
        const excelExportForm = document.getElementById('excelExportForm');
        const exportExcelBtn = document.getElementById('exportExcelBtn');
        let exportLoadingIndex = null; // 存储loading层的索引

        if (excelExportForm && exportExcelBtn) {
            excelExportForm.addEventListener('submit', function(e) {
                // 阻止默认提交行为
                e.preventDefault();

                // 显示加载提示框
                showExportLoading('Excel');

                // 禁用按钮，防止重复点击
                exportExcelBtn.disabled = true;
                exportExcelBtn.innerHTML = '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 生成中...';

                // 使用传统的form提交方式来处理文件下载
                // 创建一个隐藏的form来提交
                const hiddenForm = document.createElement('form');
                hiddenForm.method = 'POST';
                hiddenForm.action = window.location.href;
                hiddenForm.style.display = 'none';

                const exportInput = document.createElement('input');
                exportInput.type = 'hidden';
                exportInput.name = 'export';
                exportInput.value = 'excel';
                hiddenForm.appendChild(exportInput);

                document.body.appendChild(hiddenForm);

                // 提交表单
                hiddenForm.submit();

                // 清理表单
                document.body.removeChild(hiddenForm);

                // 模拟导出过程，显示进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 15 + 5; // 随机增加5-20%
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(progressInterval);

                        // 延迟关闭提示框和恢复按钮
                        setTimeout(() => {
                            hideExportLoading();
                            // 移除成功提示，直接恢复按钮状态

                            // 恢复按钮状态
                            exportExcelBtn.disabled = false;
                            exportExcelBtn.innerHTML = '<i class="layui-icon layui-icon-export"></i> 导出Excel';
                        }, 1500);
                    } else {
                        // 更新加载提示框中的进度
                        updateExportProgress('Excel', Math.round(progress));
                    }
                }, 300);
            });
        }



        // 显示导出加载提示框
        function showExportLoading(fileType) {
            if (typeof layui !== 'undefined' && layui.layer) {
                exportLoadingIndex = layui.layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    area: ['300px', '150px'],
                    skin: 'export-loading-skin',
                    content: `<div id="exportLoadingContent" style="text-align: center; padding: 20px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px; color: #1E9FFF;"></i>
                        <div id="exportStatusText" style="margin-top: 15px; font-size: 14px; color: #666;">正在生成${fileType}文件...</div>
                        <div id="exportProgressText" style="margin-top: 8px; font-size: 12px; color: #999;">准备中...</div>
                    </div>`,
                    success: function(layero) {
                        layero.find('.layui-layer-content').css({
                            'padding': '0',
                            'background-color': '#fff',
                            'border-radius': '6px'
                        });
                    }
                });
            } else {
                // 降级处理：使用原生alert
                console.log(`正在生成${fileType}文件...`);
            }
        }

        // 更新导出进度
        function updateExportProgress(fileType, progress) {
            if (typeof layui !== 'undefined' && layui.layer && exportLoadingIndex !== null) {
                const progressText = document.getElementById('exportProgressText');
                if (progressText) {
                    progressText.textContent = `进度: ${progress}%`;
                }
            }
        }

        // 隐藏导出加载提示框
        function hideExportLoading() {
            if (typeof layui !== 'undefined' && layui.layer && exportLoadingIndex !== null) {
                layui.layer.close(exportLoadingIndex);
                exportLoadingIndex = null;
            }
        }

        // 成功消息函数已移除，导出完成后不再显示提示

        // 显示错误消息
        function showErrorMessage(message) {
            if (typeof layui !== 'undefined' && layui.layer) {
                layui.layer.msg(message, {
                    icon: 2,
                    time: 3000,
                    offset: 't',
                    anim: 6
                });
            } else {
                alert(message);
            }
        }
    });
</script>
<?php
/**
 * 获取多年度投档线信息（参考zysx_x_functions.php的实现）
 */
function getToudangInfoMultiYears($conn, $college_name, $subject_type, $batch) {
    // 获取近3年的投档表
    $recent_tdx_tables = getRecentToudangTables($conn, 3);
    
    if (empty($recent_tdx_tables)) {
        return '暂无投档线数据';
    }
    
    $all_years_info = [];
    
    foreach ($recent_tdx_tables as $tdx_table) {
        $tdx_info = getToudangInfoFromTable($conn, $tdx_table, $college_name, $subject_type, $batch);
        if ($tdx_info && !empty($tdx_info['data'])) {
            $year = $tdx_info['year'];
            $data = $tdx_info['data'];
            
            // 计算该院校在该科类、批次下的整体投档信息
            $all_scores = array_column($data, '投档线');
            $all_ranks = array_column($data, '最低投档排名');
            
            // 过滤有效的分数（非空且大于0）
            $validScores = array_filter($all_scores, function ($score) {
                return !empty($score) && is_numeric($score) && $score > 0;
            });
            
            $yearInfo = "{$year}年:";
            
            // 只有当有有效分数时才显示分数信息
            if (!empty($validScores)) {
                $maxScore = max($validScores);
                $minScore = min($validScores);
                $avgScore = round(array_sum($validScores) / count($validScores), 1);
                $yearInfo .= " 最高{$maxScore}分 最低{$minScore}分 平均{$avgScore}分";
            }
            
            // 过滤有效的排名（非空且大于0）
            $validRanks = array_filter($all_ranks, function ($rank) {
                return !empty($rank) && is_numeric($rank) && $rank > 0;
            });
            
            if (!empty($validRanks)) {
                $maxRank = max($validRanks);
                $minRank = min($validRanks);
                $avgRank = round(array_sum($validRanks) / count($validRanks), 0);
                $yearInfo .= " 排名{$minRank}-{$maxRank} 平均{$avgRank}名";
            }
            
            $all_years_info[] = $yearInfo;
        }
    }
    
    if (empty($all_years_info)) {
        return '该院校在该科类、批次下无投档线数据';
    }
    
    return implode("\n", $all_years_info);
}

/**
 * 获取多年度招生计划信息（参考zysx_x_functions.php的实现）
 */
function getZsjhInfoMultiYears($conn, $college_name, $subject_type, $batch) {
    // 获取近3年的招生计划表
    $recent_zsjh_tables = getRecentZsjhTables($conn, 3);
    
    if (empty($recent_zsjh_tables)) {
        return '暂无招生计划数据';
    }
    
    $all_years_info = [];
    
    foreach ($recent_zsjh_tables as $zsjh_table) {
        $zsjh_info = getZsjhInfoFromTable($conn, $zsjh_table, $college_name, $subject_type, $batch);
        if ($zsjh_info && !empty($zsjh_info)) {
            // 提取年份
            if (preg_match('/zsjh(\\d{4})/', $zsjh_table, $m)) {
                $year = $m[1];
            } else {
                $year = '';
            }
            
            // 计算该院校在该科类、批次下的总计划数
            $total_plan = 0;
            foreach ($zsjh_info as $group_name => $items) {
                $total_plan += array_sum(array_column($items, '计划数'));
            }
            
            if ($total_plan > 0) {
                $all_years_info[] = "{$year}年: 总计划数 {$total_plan}人";
            }
        }
    }
    
    if (empty($all_years_info)) {
        return '该院校在该科类、批次下无招生计划数据';
    }
    
    return implode("\n", $all_years_info);
}

/**
 * 获取近N年的投档线表名数组
 */
function getRecentToudangTables($conn, $count = 3) {
    $year = date('Y') + 1;
    $min_year = 2024; // 可根据实际最早年份调整
    $tables = [];
    
    while ($year >= $min_year && count($tables) < $count) {
        $table = 'tdx' . $year;
        $check = mysqli_query($conn, "SHOW TABLES LIKE '".$table."'");
        if (mysqli_num_rows($check) > 0) {
            $tables[] = $table;
        }
        $year--;
    }
    
    return $tables;
}

/**
 * 获取近N年的招生计划表名数组
 */
function getRecentZsjhTables($conn, $count = 3) {
    $year = date('Y') + 1;
    $min_year = 2024; // 可根据实际最早年份调整
    $tables = [];
    
    while ($year >= $min_year && count($tables) < $count) {
        $table = 'zsjh' . $year;
        $check = mysqli_query($conn, "SHOW TABLES LIKE '".$table."'");
        if (mysqli_num_rows($check) > 0) {
            $tables[] = $table;
        }
        $year--;
    }
    
    return $tables;
}

/**
 * 从指定投档线表获取投档信息
 */
function getToudangInfoFromTable($conn, $table, $college_name, $subject_type, $batch) {
    $sql = "SELECT * FROM `$table` WHERE `院校名称`=? AND `科类`=? AND `批次`=? ORDER BY `投档线` DESC";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return null;
    }
    
    $stmt->bind_param("sss", $college_name, $subject_type, $batch);
    if (!$stmt->execute()) {
        $stmt->close();
        return null;
    }
    
    $result = $stmt->get_result();
    $tdinfos = [];
    while ($row = $result->fetch_assoc()) {
        $tdinfos[] = $row;
    }
    $stmt->close();
    
    if (count($tdinfos) > 0) {
        // 按投档排名升序排序专业组数据
        usort($tdinfos, function($a, $b) {
            $rankA = intval($a['最低投档排名'] ?? 0);
            $rankB = intval($b['最低投档排名'] ?? 0);
            return $rankA - $rankB;
        });
        
        // 返回表名中的年份
        if (preg_match('/tdx(\\d{4})/', $table, $m)) {
            $year = $m[1];
        } else {
            $year = '';
        }
        return ['year'=>$year, 'data'=>$tdinfos];
    }
    return null;
}

/**
 * 从指定招生计划表获取招生计划信息
 */
function getZsjhInfoFromTable($conn, $table, $college_name, $subject_type, $batch) {
    $sql = "SELECT * FROM `$table` WHERE `院校名称`=? AND `科类`=? AND `批次`=?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return null;
    }
    
    $stmt->bind_param("sss", $college_name, $subject_type, $batch);
    if (!$stmt->execute()) {
        $stmt->close();
        return null;
    }
    
    $result = $stmt->get_result();
    $groups = [];
    while ($row = $result->fetch_assoc()) {
        $group = $row['专业组名称'] ?: '未分组';
        $groups[$group][] = $row;
    }
    $stmt->close();
    
    return $groups;
}
?>

