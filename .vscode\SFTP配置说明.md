# SFTP 插件配置说明

## 配置文件位置
`.vscode/sftp.json`

## 主要配置参数说明

### 基础连接配置
- **name**: 连接名称，用于标识不同的服务器
- **host**: 服务器IP地址或域名
- **port**: SSH端口，默认22
- **username**: 服务器用户名
- **password**: 服务器密码（如果使用密钥认证，此项可为空）

### 密钥认证配置（推荐）
- **privateKeyPath**: 私钥文件路径（如：`C:/Users/<USER>/.ssh/id_rsa`）
- **passphrase**: 私钥密码（如果有的话）

### 路径配置
- **remotePath**: 远程服务器上的目标目录路径

### 同步配置
- **uploadOnSave**: 保存文件时自动上传
- **syncMode**: 同步模式
  - `update`: 仅更新修改的文件
  - `full`: 完全同步
  - `mirror`: 镜像同步

### 忽略文件配置
- **ignore**: 不上传的文件和目录列表

### 监控配置
- **watcher**: 文件监控设置
  - **autoUpload**: 自动上传
  - **autoDelete**: 自动删除

## 使用步骤

1. **修改配置文件**
   - 将 `your-server-ip` 替换为实际服务器IP
   - 将 `your-username` 替换为实际用户名
   - 将 `your-password` 替换为实际密码
   - 将 `remotePath` 替换为实际远程目录

2. **测试连接**
   - 使用快捷键 `Ctrl+Shift+P` 打开命令面板
   - 输入 `SFTP: Test Connection` 测试连接

3. **上传文件**
   - 右键文件/文件夹选择 `SFTP: Upload`
   - 或使用快捷键 `Ctrl+Alt+U`

4. **下载文件**
   - 右键文件/文件夹选择 `SFTP: Download`
   - 或使用快捷键 `Ctrl+Alt+D`

5. **同步目录**
   - 使用 `SFTP: Sync Local -> Remote` 同步到服务器
   - 使用 `SFTP: Sync Remote -> Local` 从服务器同步

## 安全建议

1. **使用密钥认证**：比密码认证更安全
2. **限制用户权限**：服务器用户只读权限
3. **定期更换密码**：定期更新服务器密码
4. **防火墙设置**：只允许特定IP访问SSH端口

## 常见问题

### 连接失败
- 检查服务器IP和端口是否正确
- 确认用户名和密码是否正确
- 检查服务器SSH服务是否运行
- 检查防火墙设置

### 权限问题
- 确认远程目录有写入权限
- 检查文件所有者设置

### 同步问题
- 检查ignore配置是否正确
- 确认本地和远程文件路径匹配
