@charset "utf-8";

ul,ol,li{
	list-style: none;
	margin: 0;
	padding: 0;
}

.alertDisable{
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	display: none;
	background-color: rgba(0,0,0,0.7);
	filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7F000000,endcolorstr=#7F000000);
	z-index: 99;
}
.alertBox{
	width: 90%;
	height: 15rem;
	position: absolute;
	top: 20%;
	left: 5%;
	margin-left: 0rem;
	border-radius: 1rem;
	overflow: hidden;
	background-color: #fff;
}
.alertHeader{
	font-size: 1rem;
	height: 2.5rem;
	line-height: 2.5rem;
	text-align: center;
	font-weight: bolder;
	color: #fff;
	background-color: #3eb1dd;
}
.alertContent{
	height: 7rem;
	overflow-y: auto;
	padding: 1rem;
	color: #333;
}
.alertContent p{
	font-size: 1rem;
	/*letter-spacing: 0.1rem;*/
	word-break:break-all;
	text-indent: 1.8rem;
	text-align: left;
}
.alertFooter{
	height: 3.5rem;
	text-align: center;
	line-height: 3.5rem;
	border-top: 1px solid #3eb1dd;
	/*background-color: #ccc;*/
	font-size: 0;
}
.alertFooter a{
	display: inline-block;
	vertical-align: middle;
	text-align: center;
	text-decoration: none;
	cursor: pointer;
	width: 42%;
	height: 2.5rem;
	line-height: 2.5rem;	
	color: #000;
	font-size: 1rem;
	border-radius: 0.3rem;
	background-color: #fff;
}
.alertFooter a.alertOK{
	background-color: #3eb1dd;
	color: #fff;
	/*margin-right: 5%;*/
}
.alertFooter a.alertCancle{
	border: 1px solid #3eb1dd;
	box-sizing: border-box;
	/*margin-left: 5%;*/
}
.alertFooter a:hover{
	text-decoration: none;
	/*background-color: #777;*/
}
