<?php
include "htmlhead.php";
?>
<SCRIPT language=javascript>
function CheckPost()
{
	if (myform.searchnr.value=="")
	{
		alert("请输入要查询的内容！");
		myform.searchnr.focus();
		return false;
	}
	document.forms['myform'].submit();
}
function copytitle(copymes)
{
        var input = document.createElement("input");
            input.value = copymes;
            document.body.appendChild(input);
            input.select();
            input.setSelectionRange(0, input.value.length), document.execCommand('Copy');
            document.body.removeChild(input);
            alert("复制成功");

}
</SCRIPT>
<body>
<?php
include("../conn.php");
$px=isset($_GET['px'])?$_GET['px']:'MC';
$aod=isset($_GET['aod'])?$_GET['aod']:'DESC';
if ($aod=='ASC'){$aod='DESC';}else{$aod='ASC';}
$sql="SELECT * FROM pro_list Where 1=1 ORDER BY $px $aod";
$query = mysqli_query($conn,$sql) or die("SQL语句执行失败");
if(isset($_GET['searchnr'])){
	$ssnr=$_GET['searchnr'];
	$sql= "SELECT * FROM pro_list Where BH='$ssnr' or MC like '%$ssnr%' order by ID";
	$query = mysqli_query($conn,$sql) or die("SQL语句执行失败$sql");
	$total = mysqli_num_rows($query); //记录总条数
	if($total<=0){
		echo remessage("k","未找到，请重新输入","javascript:history.back(-1);"," 返 回 ");
		exit;
	}
}
?>
<div class="right-bottom" id="gotop" style="display: none;"><img src="../images/gotop.png" width="35" height="35" border="0"></div>
<script type="text/javascript" src="../gotop.js"></script>
<form action="pro_list.php"  method="get" name="myform" onSubmit="return CheckPost();">
	<table width="98%" align="center" cellpadding="8" cellspacing="0" frame="below" rules="rows" bordercolor="#ececec" border="0">
		<tr bgcolor="#ececec">
		<td align="center">
	<table align="center" frame="box" rules="none" bordercolor="#a0a0a0"  width="340" height="35" border="1" cellpadding="8" cellspacing="0">
		<tr bgcolor="#ffffff">
			<td width="20" align="center" class="tabradl"><img src='../images/zoom.png' width='20' height='20' border='0'></td>
			<td width="290" align="left"><input type="text" name="searchnr" placeholder="请输入编号或名称关键字搜索..." style="height:100%;width:100%;border:0;outline:none;"></td>
			<td width="30" align="center" class="tabradr"><img src='../images/search.png' width='36' height='18' border='0' onclick="CheckPost();" style="cursor:pointer"></td>
		</tr>
	</table>
	</td>
      </tr>
</table>
</form>
<table width="98%" height="35" frame="below" rules="none" border="0" cellpadding="0" cellspacing="0" align="center" bordercolor="#ececec" style="font-size:0.9em;">
      <tr bgcolor="#ececec">
		<td align="center" width="25%"><a href='pro_list.php?px=ID&aod=<?= $aod?>' ><?php if ($px=='ID'){echo '<font color="red">编号</font>';}else{echo '编号';}?> <img src="./images/<?php if ($px=='ID'){if ($aod=='ASC'){echo 'arrow_a';}else{echo 'arrow_d';}}else{echo 'arrow';}?>.png" border="0" width="8" height="15" style="margin-bottom:-2px;"></a></td>
        <td align="center" width="25%"><a href='pro_list.php?px=MC&aod=<?= $aod?>' ><?php if ($px=='MC'){echo '<font color="red">名称</font>';}else{echo '名称';}?> <img src="./images/<?php if ($px=='MC'){if ($aod=='ASC'){echo 'arrow_a';}else{echo 'arrow_d';}}else{echo 'arrow';}?>.png" border="0" width="8" height="15" style="margin-bottom:-2px;"></a></td>
		<td align="center" width="25%"><a href='pro_list.php?px=PRICE&aod=<?= $aod?>' ><?php if ($px=='PRICE'){echo '<font color="red">价格</font>';}else{echo '价格';}?> <img src="./images/<?php if ($px=='PRICE'){if ($aod=='ASC'){echo 'arrow_a';}else{echo 'arrow_d';}}else{echo 'arrow';}?>.png" border="0" width="8" height="15" style="margin-bottom:-2px;"></a></td>
		<td align="center" width="25%"><a href='pro_list.php?px=BZ&aod=<?= $aod?>' ><?php if ($px=='BZ'){echo '<font color="red">设计报告</font>';}else{echo '设计报告';}?> <img src="./images/<?php if ($px=='BZ'){if ($aod=='ASC'){echo 'arrow_a';}else{echo 'arrow_d';}}else{echo 'arrow';}?>.png" border="0" width="8" height="15" style="margin-bottom:-2px;"></a></td>
      </tr>
</table>
<table width="98%" align="center" cellpadding="8" cellspacing="0" frame="below" rules="rows" bordercolor="#efefef" border="1">
<?php
  $k=0;
	while($row = mysqli_fetch_array($query)){
		$pro_id=$row['ID'];
		$pro_bh=$row['BH'];
		$pro_mc=$row['MC'];
		$pro_ver=$row['VER'];
		$pro_price=$row['PRICE'];
		$pro_bz=$row['BZ'];
		$screenshot_bh=sprintf("%03d",$pro_bh);
		$icon='screenshot/'.$screenshot_bh.'/z.jpg';
		if(!file_exists($icon)){$icon='screenshot/'.$screenshot_bh.'/z.png';};
		if ($pro_bz==''){$pro_bz='无';}
		$dis_zy='<a href=pro_show.php?bh='. $screenshot_bh.'><b>'.$pro_mc.'</b></a> <img onclick=copytitle("编号：'.$screenshot_bh.$pro_mc.'") src="images/copy.png" border="0" width="14" height="16" style="margin-bottom:-2px;"><br><a href=pro_show.php?bh='. $screenshot_bh.' class="btn titlebtn"><span style="color:#a0a0a0;font-size:0.8em;line-height:20px;">成品编号：'.$pro_bh.
			'<br>编写版本：'.$pro_ver.
			'<br>设计报告：'.$pro_bz.
			'<br>价格：<font size="4" color="red">'.$pro_price.'</font>元</span></a>';
?>
	<tr bgcolor="#ffffff">
		<td width="30%" align="center" style="padding:4px 4px 4px 4px;"><a href=pro_show.php?bh=<?= $screenshot_bh ?>><img src='<?=$icon?>' width='120' height='120' border='0'></a></td>
		<td width="70%" style="padding:2px 0px 0px 6px;"> <?= $dis_zy ?></td>
	</tr>
<?php
	}
?>
</table>
<Br>
<br>
<?php
//include("dhtool.php");
?>
</body>
</html>