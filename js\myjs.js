function yxsc(param1, param2){
	var str=param1;
	var yxscid=param2;
	//console.log('cz_nohead.php?cz=scyx_jq&yxscid='+yxscid+str);
	$.ajax({
		type: 'GET', // 请求类型，根据需要也可以是'POST'
		url: 'cz_nohead.php?cz=scyx_jq&yxscid='+yxscid+str, // PHP后端脚本地址'
		data:{},
		dataType: 'html', // 期望从服务器返回的数据类型
		success: function(response) {
			// 请求成功后的回调函数
			var sfsc_img = $(response).filter('#fragment1').html();
			var sfsc_text = $(response).filter('#fragment2').html();
			var div_id = $(response).filter('#fragment3').html();
			$('#'+div_id).html(sfsc_img);
			layer.msg(sfsc_text);
		},
		error:function(data){
			layer.msg('提示','服务器错误！');
	　　},
		beforeSend:function(data){
			//console.log('加载中……');
		}
	});
}