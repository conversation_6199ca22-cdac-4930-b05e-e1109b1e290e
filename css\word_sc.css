/**
 * 收藏夹页面样式文件
 * 文件名：word_sc.css
 * 创建时间：2024年12月19日
 * 重构时间：2024年12月19日
 * 
 * 包含收藏夹页面的所有样式定义
 */

/* ==================== 基础样式 ==================== */
* {
    -webkit-tap-highlight-color: transparent;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ==================== 收藏统计头部样式 ==================== */
.favorite-header {
    background: #fff;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #e0e0e0;
}

/* 统计表格样式 */
.favorite-stats-table {
    margin-bottom: 10px;
}

.stats-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-table th,
.stats-table td {
    padding: 6px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    vertical-align: middle;
}

.stats-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 0.95em;
    border-bottom: 1px solid #e0e0e0;
}

.stats-table th:last-child,
.stats-table td:last-child {
    border-right: none;
}

.stats-table tr:last-child td {
    border-bottom: none;
}

.stats-table tbody tr:hover {
    background: #f8f9fa;
}

.stats-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.stats-table tbody tr:nth-child(even):hover {
    background: #f0f1f2;
}

/* 合并单元格的样式优化 */
.stats-table td[rowspan] {
    background: #f8f9fa;
    font-weight: 500;
    color: #333; /* 科类恢复原来的颜色 */
}

.stats-table td[rowspan]:hover {
    background: #e8f9f7;
}

/* 批次列样式 - 加粗绿色 */
.stats-table td.batch-cell {
    font-weight: bold !important;
    color: #16baaa !important; /* 绿色 */
    cursor: pointer !important; /* 添加指针样式表示可点击 */
    transition: color 0.3s ease;
}

.stats-table td.batch-cell:hover {
    color: #16baaa !important; /* 悬停时深绿色 */
    text-decoration: underline !important; /* 悬停时添加下划线 */
}

/* 科类列样式 - 恢复原来的颜色 */
.stats-table td:first-child {
    color: #333;
    font-weight: 500;
}

/* 院校信息列靠左对齐 */
.stats-table td.college-info {
    text-align: left !important;
    padding-left: 10px;
    padding-right: 10px;
}

/* 排名信息列靠左对齐 */
.stats-table td.ranking-info {
    text-align: left !important;
    padding-left: 10px;
    padding-right: 10px;
}

/* 投档线信息列靠左对齐 */
.stats-table td.toudang-info {
    text-align: left !important;
    padding-left: 10px;
    padding-right: 10px;
    white-space: pre-line; /* 保持换行符 */
}

/* 招生计划信息列靠左对齐 */
.stats-table td.zsjh-info {
    text-align: left !important;
    padding-left: 10px;
    padding-right: 10px;
    white-space: pre-line; /* 保持换行符 */
}

/* 院校行样式 */
.stats-table tr.college-row {
    background: #f0f9f8 !important;
    font-weight: bold;
}

.stats-table tr.college-row td {
    background: #f0f9f8 !important;
    border-bottom: 1px solid #d1ecf1;
    vertical-align: top;
}

/* 专业行样式 */
.stats-table tr.major-row {
    background: #ffffff;
}

.stats-table tr.major-row td {
    background: #ffffff;
    vertical-align: top;
    border-bottom: 1px solid #dee2e6;
}

.stats-table tr.major-row:hover td {
    background: #f8f9fa;
}

/* 表格标题样式 */
.table-title {
    text-align: center;
    padding: 10px;
    border-radius: 8px;
}

.table-title h4 {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
    line-height: 1.5;
}

.table-title h3 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    line-height: 1.5;
}

/* 确保表格边框一致性 */
.stats-table {
    border-collapse: collapse;
    width: 100%;
}

.stats-table th,
.stats-table td {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    text-align: center;
}

.loading-text {
    color: #888;
    font-style: italic;
}

.no-data {
    color: #999;
    font-style: italic;
}

.favorite-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    justify-content: center;
}

.stat-item {
    text-align: center;
    flex: 1;
    max-width: 200px;
}

.stat-number {
    display: block;
    font-size: 2.5em;
    font-weight: bold;
    color: #16baaa;
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

.favorite-actions {
    display: flex;
    gap: 10px;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 100%;
}

/* ==================== 按钮样式 ==================== */
.favorite-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    height: 40px;
    flex: 1;
    min-width: 0;
    position: relative;
    overflow: hidden;
}

.favorite-btn .layui-icon {
    margin-right: 5px;
    font-size: 14px;
}

.favorite-btn-primary {
    background: #16baaa;
    color: #fff;
}

.favorite-btn-primary:hover {
    background: #13a89a;
}

.favorite-btn-secondary {
    background: #6c757d;
    color: #fff;
}

.favorite-btn-secondary:hover {
    background: #5a6268;
}

/* 志愿加载按钮样式 - 橙色背景 */
.favorite-btn-loading {
    background: #ffb800;
    color: #fff;
}

.favorite-btn-loading:hover {
    background: #e6a600;
}

.favorite-btn-danger {
    background: #ff4757;
    color: #fff;
}

.favorite-btn-danger:hover {
    background: #e63946;
}

/* 收藏按钮在专业卡片中的样式 */
.favorite-btn.favorite-inactive {
    border: 1px solid #ddd;
    background: #fff;
    color: #999;
}

.favorite-btn.favorite-active {
    border: 1px solid #ff6b6b;
    background: #ff6b6b;
    color: #fff;
}

.favorite-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.favorite-btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ==================== 收藏列表样式 ==================== */
.favorite-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.favorite-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.favorite-item {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e0e0e0;
}

.item-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.item-index {
    background: #16baaa;
    color: #fff;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9em;
    flex-shrink: 0;
}

.item-college {
    font-size: 1.2em;
    font-weight: bold;
    color: #333;
    flex: 1;
}

.item-major {
    font-size: 1.1em;
    color: #16baaa;
    font-weight: 500;
    flex: 1;
}

.item-details {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.detail-badge {
    background: rgba(22, 186, 170, 0.1);
    color: #16baaa;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    border: 1px solid rgba(22, 186, 170, 0.3);
}

.item-actions {
    display: flex;
    justify-content: flex-end;
}

/* ==================== 加载状态样式 ==================== */
.loading-state {
    text-align: center;
    padding: 60px 20px;
    color: #888;
}

.loading-spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #16baaa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 空状态样式 ==================== */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #888;
}

.empty-state .layui-icon {
    font-size: 5em;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 1.5em;
}

.empty-state p {
    margin: 0;
    line-height: 1.6;
    color: #999;
    font-size: 1.1em;
}

/* ==================== 消息提示样式 ==================== */
.message-error {
    text-align: center;
    padding: 40px 20px;
    color: #ff4757;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #ffebee;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-success {
    text-align: center;
    padding: 40px 20px;
    color: #52c41a;
    background: #f6ffed;
    border-radius: 8px;
    border: 1px solid #b7eb8f;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* ==================== 批次收藏院校显示样式 ==================== */
.batch-header {
    background: #fff;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    text-align: center;
}

.batch-header h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.batch-header p {
    margin: 0;
    color: #666;
    font-size: 0.9em;
}

.college-group {
    background: #fff;
    border-radius: 5px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.college-header {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.college-index {
    background: #16baaa;
    color: #fff;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    font-weight: bold;
}

.college-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1em;
    flex: 1;
}

.college-count {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.major-list {
    padding: 0;
}

.major-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
}

.major-item:last-child {
    border-bottom: none;
}

.major-item:hover {
    background: #f8f9fa;
}

.major-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.major-index {
    background: #6c757d;
    color: #fff;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7em;
    font-weight: bold;
}

.major-name {
    font-weight: 500;
    color: #333;
    font-size: 1em;
}

.major-group {
    background: #e3f2fd;
    color: #1976d2;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    font-weight: 500;
}

.major-actions {
    flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .college-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .major-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .major-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .major-actions {
        width: 100%;
    }
    
    .major-actions .favorite-btn {
        width: 100%;
    }
}

/* ==================== 院校卡片样式 ==================== */
/* 院校卡片容器 - 基础样式 */
.college-card {
    border: 1px solid #e0e0e0;
    padding: 15px;
    margin-bottom: 5px;
    border-radius: 8px;
    background: #fff;
    position: relative;
}

/* 院校标题 - 主要显示信息 */
.college-title {
    font-size: 1.3em;
    font-weight: bold;
    color: #222;
    margin-bottom: 5px;
    z-index: 3;
}

/* 院校徽章 - 显示院校类型或特色 */
.college-badge {
    display: inline-block;
    background: #16baaa;
    color: #fff;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 0.85em;
    margin-right: 6px;
    margin-bottom: 4px;
}

/* 院校元信息 - 显示院校基本属性 */
.college-meta {
    font-weight: bold;
    color: #444;
    margin-top: 5px;
    font-size: 0.95em;
}

/* 院校排名 - 显示排名信息 */
.college-rank {
    margin: 5px 0px;
    color: #888;
    font-size: 0.98em;
}

/* 院校索引徽章 - 右上角显示序号 */
.college-index-badge {
    position: absolute;
    top: 0;
    right: 35px;
    background: #16baaa;
    color: #fff;
    border-radius: 0 0 8px 8px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.95em;
    font-weight: bold;
    z-index: 2;
}

/* 院校删除按钮 - 右上角删除功能 */
.college-delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 0 8px 0 8px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
}

.college-delete-btn:hover {
    background: #ff3742;
    transform: scale(1.1);
}

/* 院校会员提示徽章 - 显示会员状态 */
.college-hyts-badge {
    position: absolute;
    top: 0;
    right: 70px;
    background: #ff9800;
    color: #fff;
    border-radius: 0 0 8px 8px;
    padding: 2px 8px;
    height: 28px;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85em;
    font-weight: bold;
}

/* 排名徽章 - 显示院校排名信息 */
.rank-badge {
    display: inline-block;
    background-color: rgba(22, 186, 170, 0.1);
    border: 1px solid rgba(22, 186, 170, 0.5);
    color: rgba(22, 186, 170);
    border-radius: 3px;
    padding: 0px 4px;
    font-size: 0.85em;
    margin-right: 6px;
    margin-bottom: 4px;
    margin-top: 5px;
}

/* ==================== 专业卡片样式 ==================== */
/* 专业卡片列表容器 */
.major-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 8px;
}

/* 专业卡片 - 单个专业信息显示 */
.major-card {
    background: #f8f8f8;
    border-radius: 5px;
    padding: 10px 12px;
    flex: 1 1 100%;
    min-width: 100%;
    border: 1px solid #e0e0e0;
}

/* ==================== 批次头部样式 ==================== */
/* 批次头部容器 - 显示批次信息 */
.batch-header {
    background: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 5px;
    border: 1px solid #e0e0e0;
}

.batch-header h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1.4em;
}

.batch-header p {
    margin: 0;
    color: #666;
    font-size: 1em;
}

/* ==================== 投档信息和招生计划样式 ==================== */
/* 批次标题 - 投档信息、招生计划、收藏专业等 */
.toudang-batch-title,
.zsjh-batch-title,
.favorites-batch-title {
    font-size: 1.08em;
    font-weight: bold;
    color: #888;
    background: #f8f8f8;
    border-radius: 4px;
    padding: 5px 8px;
    position: relative;
    margin-top: 5px;
    cursor: pointer;
}

/* 卡片列表容器 - 投档信息、招生计划、收藏专业等 */
.toudang-card-list,
.zsjh-card-list,
.favorites-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
}

/* 投档信息卡片 */
.toudang-card {
    background: #f8f8f8;
    border-radius: 5px;
    padding: 10px 12px;
    flex: 1 1 100%;
    min-width: 100%;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid #e0e0e0;
}

/* ==================== 投档信息及招生计划二级标题样式 ==================== */
/* 二级标题容器 - 投档信息、招生计划、收藏专业等 */
.toudang-card,
.zsjh-group-title,
.favorites-group-title {
    background: rgba(22, 186, 170, 0.05);
    border: 1px solid rgba(22, 186, 170, 0.5);
}

/* 专业详情卡片列表 */
.major-detail-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
}

/* 专业详情卡片、招生计划卡片、收藏卡片 */
.major-detail-card,
.zsjh-card,
.favorites-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
    padding: 10px;
    padding-right: 14px;
    border-radius: 5px;
    position: relative;
}

/* 专业组单元格 - 可点击的专业组信息 */
.zygroup-cell {
    cursor: pointer;
    color: #16baaa;
    font-weight: bold;
    font-size: 1.08em;
    margin-bottom: 8px;
}

/* ==================== 专业组标题样式 ==================== */
/* 专业组标题 - 招生计划和收藏专业的分组标题 */
.zsjh-group-title,
.favorites-group-title {
    font-size: 1.08em;
    cursor: pointer;
    color: #16baaa;
    font-weight: bold;
    margin: 4px 0;
    padding: 5px 12px;
    background: #f8f8f8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding-left: 18px; /* 确保与专业卡片内容左对齐 */
}

/* 箭头图标 - 展开/折叠指示器 */
.arrow-span {
    color: #16baaa;
    font-size: 16px;
    vertical-align: middle;
    flex-shrink: 0;
    margin-left: auto; /* 右对齐 */
}

/* ==================== 复选框样式 ==================== */
/* 收藏复选框和专业组复选框 */
.favorite-checkbox,
.group-checkbox {
    cursor: pointer;
    margin-right: 8px;
    border: 1px solid #16baaa;
    vertical-align: middle;
    margin-top: 0;
    margin-bottom: 0;
    flex-shrink: 0; /* 防止被压缩 */
}

/* 复选框选中状态 */
.favorite-checkbox:checked,
.group-checkbox:checked {
    accent-color: #16baaa;
    border: 1px solid #16baaa;
}

/* ==================== 批量操作按钮样式 ==================== */
/* 警告类型按钮 - 批量删除等 */
.favorite-btn-warning {
    background-color: #ff9800;
    border-color: #ff9800;
    color: #fff;
}

.favorite-btn-warning:hover {
    background-color: #f57c00;
    border-color: #f57c00;
}

/* ==================== 收藏夹操作按钮样式 ==================== */
/* 收藏夹操作按钮 - 批量删除、清空、导出等 */
.favorite-action-btn {
    padding: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    width: 100%;
}

.favorite-action-btn:last-child {
    margin-right: 0;
}

/* 志愿预览按钮样式 - 始终显示橙色背景 */
#togglePreviewTable.favorite-action-btn {
    background: #ffb800;
    color: #fff;
}

#togglePreviewTable.favorite-action-btn:hover {
    background: #e6a600;
}

#togglePreviewTable.favorite-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* ==================== 收藏卡片复选框容器 ==================== */
/* 收藏卡片复选框容器 - 确保复选框与文字对齐 */
.favorite-checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    gap: 5px;
    padding-left: 0; /* 确保与专业组标题文字左对齐 */
}

.favorite-checkbox-container .favorite-checkbox {
    margin-right: 0;
}

/* ==================== 专业卡片删除按钮样式 ==================== */
/* 专业卡片删除按钮 - 右上角X按钮 */
.favorite-delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 0 5px 0 5px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
}

.favorite-delete-btn:hover {
    background: #ff0000;
    transform: scale(1.1);
}

/* ==================== 院校卡片样式 ==================== */
/* 院校卡片容器 - 基础样式 */
.college-card {
    border: 1px solid #e0e0e0;
    padding: 15px;
    margin-bottom: 5px;
    border-radius: 8px;
    background: #fff;
    position: relative;
}

/* 院校标题 - 主要显示信息 */
.college-title {
    font-size: 1.3em;
    font-weight: bold;
    color: #222;
    margin-bottom: 5px;
    z-index: 3;
}

/* 院校徽章 - 显示院校类型或特色 */
.college-badge {
    display: inline-block;
    background: #16baaa;
    color: #fff;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 0.85em;
    margin-right: 6px;
    margin-bottom: 4px;
}

/* 院校元信息 - 显示院校基本属性 */
.college-meta {
    font-weight: bold;
    color: #444;
    margin-top: 5px;
    font-size: 0.95em;
}

/* 院校排名 - 显示排名信息 */
.college-rank {
    margin: 5px 0px;
    color: #888;
    font-size: 0.98em;
}

/* 院校索引徽章 - 右上角显示序号 */
.college-index-badge {
    position: absolute;
    top: 0;
    right: 35px;
    background: #16baaa;
    color: #fff;
    border-radius: 0 0 8px 8px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.95em;
    font-weight: bold;
    z-index: 2;
}

/* 院校删除按钮 - 右上角删除功能 */
.college-delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 0 8px 0 8px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
}

.college-delete-btn:hover {
    background: #ff3742;
    transform: scale(1.1);
}

/* 院校会员提示徽章 - 显示会员状态 */
.college-hyts-badge {
    position: absolute;
    top: 0;
    right: 70px;
    background: #ff9800;
    color: #fff;
    border-radius: 0 0 8px 8px;
    padding: 2px 8px;
    height: 28px;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85em;
    font-weight: bold;
}

/* 排名徽章 - 显示院校排名信息 */
.rank-badge {
    display: inline-block;
    background-color: rgba(22, 186, 170, 0.1);
    border: 1px solid rgba(22, 186, 170, 0.5);
    color: rgba(22, 186, 170);
    border-radius: 3px;
    padding: 0px 4px;
    font-size: 0.85em;
    margin-right: 6px;
    margin-bottom: 4px;
    margin-top: 5px;
}

/* ==================== 专业卡片样式 ==================== */
/* 专业卡片列表容器 */
.major-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 8px;
}

/* 专业卡片 - 单个专业信息显示 */
.major-card {
    background: #f8f8f8;
    border-radius: 5px;
    padding: 10px 12px;
    flex: 1 1 100%;
    min-width: 100%;
    border: 1px solid #e0e0e0;
}

/* ==================== 批次头部样式 ==================== */
/* 批次头部容器 - 显示批次信息 */
.batch-header {
    background: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 5px;
    border: 1px solid #e0e0e0;
}

.batch-header h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1.4em;
}

.batch-header p {
    margin: 0;
    color: #666;
    font-size: 1em;
}

/* ==================== 投档信息和招生计划样式 ==================== */
/* 批次标题 - 投档信息、招生计划、收藏专业等 */
.toudang-batch-title,
.zsjh-batch-title,
.favorites-batch-title {
    font-size: 1.08em;
    font-weight: bold;
    color: #888;
    background: #f8f8f8;
    border-radius: 4px;
    padding: 5px 8px;
    position: relative;
    margin-top: 5px;
    cursor: pointer;
}

/* 卡片列表容器 - 投档信息、招生计划、收藏专业等 */
.toudang-card-list,
.zsjh-card-list,
.favorites-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
}

/* 投档信息卡片 */
.toudang-card {
    background: #f8f8f8;
    border-radius: 5px;
    padding: 10px 12px;
    flex: 1 1 100%;
    min-width: 100%;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid #e0e0e0;
}

/* ==================== 投档信息及招生计划二级标题样式 ==================== */
/* 二级标题容器 - 投档信息、招生计划、收藏专业等 */
.toudang-card,
.zsjh-group-title,
.favorites-group-title {
    background: rgba(22, 186, 170, 0.05);
    border: 1px solid rgba(22, 186, 170, 0.5);
}

/* 专业详情卡片列表 */
.major-detail-card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
}

/* 专业详情卡片、招生计划卡片、收藏卡片 */
.major-detail-card,
.zsjh-card,
.favorites-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
    padding: 10px;
    padding-right: 14px;
    border-radius: 5px;
    position: relative;
}

/* 专业组单元格 - 可点击的专业组信息 */
.zygroup-cell {
    cursor: pointer;
    color: #16baaa;
    font-weight: bold;
    font-size: 1.08em;
    margin-bottom: 8px;
}

/* ==================== 专业组标题样式 ==================== */
/* 专业组标题 - 招生计划和收藏专业的分组标题 */
.zsjh-group-title,
.favorites-group-title {
    font-size: 1.08em;
    cursor: pointer;
    color: #16baaa;
    font-weight: bold;
    margin: 4px 0;
    padding: 5px 12px;
    background: #f8f8f8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding-left: 18px; /* 确保与专业卡片内容左对齐 */
}

/* 箭头图标 - 展开/折叠指示器 */
.arrow-span {
    color: #16baaa;
    font-size: 16px;
    vertical-align: middle;
    flex-shrink: 0;
    margin-left: auto; /* 右对齐 */
}

/* ==================== 复选框样式 ==================== */
/* 收藏复选框和专业组复选框 */
.favorite-checkbox,
.group-checkbox {
    cursor: pointer;
    margin-right: 8px;
    border: 1px solid #16baaa;
    vertical-align: middle;
    margin-top: 0;
    margin-bottom: 0;
    flex-shrink: 0; /* 防止被压缩 */
}

/* 复选框选中状态 */
.favorite-checkbox:checked,
.group-checkbox:checked {
    accent-color: #16baaa;
    border: 1px solid #16baaa;
}

/* ==================== 批量操作按钮样式 ==================== */
/* 警告类型按钮 - 批量删除等 */
.favorite-btn-warning {
    background-color: #ff9800;
    border-color: #ff9800;
    color: #fff;
}

.favorite-btn-warning:hover {
    background-color: #f57c00;
    border-color: #f57c00;
}

/* ==================== 收藏夹操作按钮样式 ==================== */
/* 收藏夹操作按钮 - 批量删除、清空、导出等 */
.favorite-action-btn {
    padding: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    width: 100%;
}

.favorite-action-btn:last-child {
    margin-right: 0;
}

/* 志愿预览按钮样式 - 始终显示橙色背景 */
#togglePreviewTable.favorite-action-btn {
    background: #ffb800;
    color: #fff;
}

#togglePreviewTable.favorite-action-btn:hover {
    background: #e6a600;
}

#togglePreviewTable.favorite-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* ==================== 收藏卡片复选框容器 ==================== */
/* 收藏卡片复选框容器 - 确保复选框与文字对齐 */
.favorite-checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    gap: 5px;
    padding-left: 0; /* 确保与专业组标题文字左对齐 */
}

.favorite-checkbox-container .favorite-checkbox {
    margin-right: 0;
}

/* ==================== 专业卡片删除按钮样式 ==================== */
/* 专业卡片删除按钮 - 右上角X按钮 */
.favorite-delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 0 5px 0 5px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
}

.favorite-delete-btn:hover {
    background: #ff0000;
    transform: scale(1.1);
}

/* ==================== 预览表格样式 ==================== */
.preview-table-container {
    margin-top: 15px;
    overflow-x: auto;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-table-container .stats-table {
    margin-bottom: 0;
}

.preview-table-container .stats-table th {
    background: #16baaa;
    color: #fff;
    font-weight: 600;
    text-align: center;
    padding: 12px 8px;
}

.preview-table-container .stats-table td {
    padding: 10px 8px;
    text-align: center;
    vertical-align: middle;
}

.preview-table-container .stats-table tbody tr:hover {
    background: #f0f9f8;
}

.preview-table-container .stats-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.preview-table-container .stats-table tbody tr:nth-child(even):hover {
    background: #f0f9f8;
}

/* ==================== 专业信息列样式 ==================== */
.major-info-column {
    transition: all 0.3s ease;
}

.major-info-column.hidden {
    display: none !important;
}

/* 导出按钮容器样式 */
.export-button-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 10px;
    margin-bottom: 20px;
    justify-content: space-between;
    flex-wrap: nowrap;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.export-button-container .favorite-action-btn {
    flex: 1;
    white-space: nowrap;
    width: 100%;
    padding: 10px 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.export-button-container form {
    margin: 0;
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .preview-table-container {
        overflow-x: auto;
    }
    
    .preview-table-container .stats-table {
        min-width: 600px;
    }
    
    .preview-table-container .stats-table th,
    .preview-table-container .stats-table td {
        padding: 8px 6px;
        font-size: 13px;
    }
}
