/**
 * 收藏功能JavaScript（重构版）
 * 文件名：word_sc.js
 * 创建时间：2024年12月19日
 * 重构时间：2024年12月19日
 * 
 * 参考zysx_x_functions.php的实现方式
 * 使用新的单表word_sc结构
 */

// 收藏功能全局变量
window.favoriteSystem = {
    // 初始化收藏系统
    init: function() {
        this.bindEvents();
        this.updateFavoriteButtons();
    },

    // 绑定事件
    bindEvents: function() {
        // 收藏按钮点击事件
        $(document).on('click', '.favorite-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var collegeName = decodeURIComponent($btn.data('college-name') || '');
            var majorName = decodeURIComponent($btn.data('major-name') || '');
            var majorCode = decodeURIComponent($btn.data('major-code') || '');
            var year = $btn.data('year') || new Date().getFullYear();
            
            if (!collegeName || !majorName) {
                layer.msg('收藏信息不完整', {icon: 2});
                return;
            }
            
            // 检查登录状态
            if (!window.userLoggedIn || !window.dlusername || window.dlusername.trim() === '') {
                layer.msg('请先登录后再使用收藏功能', {icon: 2});
                return;
            }
            
            // 执行收藏/取消收藏操作
            favoriteSystem.toggleFavorite($btn, collegeName, majorCode, majorName, year);
        });
    },

    // 切换收藏状态
    toggleFavorite: function($btn, collegeName, majorCode, majorName, year) {
        var isFavorite = $btn.hasClass('favorite-active');
        var action = isFavorite ? 'remove_favorite' : 'add_favorite';
        
        // 显示加载状态
        $btn.addClass('loading').prop('disabled', true);
        
        // 准备请求数据
        var requestData = {
            action: action,
            college_name: collegeName,
            major_code: majorCode,
            major_name: majorName,
            year: year
        };
        
        // 如果是添加收藏，需要更多信息
        if (action === 'add_favorite') {
            var $card = $btn.closest('.zsjh-card');
            if ($card.length > 0) {
                // 使用新的字段映射
                requestData.college_code = $btn.data('college-code') || collegeName;
                requestData.subject_type = $btn.data('subject-type') || ''; // 科类
                requestData.batch = $btn.data('batch') || ''; // 批次
                requestData.major_group = $btn.data('major-group') || '';
                requestData.major_group_code = $btn.data('major-group-code') || '';
                requestData.major_code = $btn.data('major-code') || '';
                requestData.plan_count = $btn.data('plan-count') || '';
                requestData.fee = $btn.data('fee') || '';
                requestData.subject_req = $btn.data('subject-req') || '';
                requestData.major_note = $btn.data('major-note') || '';
            }
        }
        
        // 发送请求
        $.ajax({
            url: 'word_sc_api.php',
            type: 'POST',
            data: requestData,
            dataType: 'json',
            timeout: 10000,
            success: function(response) {
                if (response.success) {
                    // 立即更新当前按钮的收藏状态
                    if (!isFavorite) {
                        // 收藏成功后，立即将按钮状态改为取消收藏
                        $btn.addClass('favorite-active').removeClass('favorite-inactive');
                        $btn.html('<i class="layui-icon layui-icon-heart-fill"></i> 取消收藏');
                        $btn.attr('title', '取消收藏');
                    } else {
                        // 取消收藏成功后，立即将按钮状态改为未收藏
                        $btn.addClass('favorite-inactive').removeClass('favorite-active');
                        $btn.html('<i class="layui-icon layui-icon-heart"></i> 添加到收藏夹');
                        $btn.attr('title', '添加到收藏夹');
                    }
                    
                    // 显示成功消息
                    var message = isFavorite ? '取消收藏成功' : '收藏成功';
                    layer.msg(message, {icon: 1});
                    
                    // 异步更新收藏数量显示
                    setTimeout(function() {
                        favoriteSystem.updateFavoriteCount();
                    }, 100);
                    
                } else {
                    // 显示错误消息
                    layer.msg(response.message || '操作失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请稍后再试', {icon: 2});
            },
            complete: function() {
                // 移除加载状态
                $btn.removeClass('loading').prop('disabled', false);
            }
        });
    },

    // 更新按钮状态
    updateButtonState: function($btn, isFavorite) {
        if (isFavorite) {
            $btn.removeClass('favorite-inactive').addClass('favorite-active');
            $btn.html('<i class="layui-icon layui-icon-heart-fill"></i> 取消收藏');
            $btn.attr('title', '取消收藏');
        } else {
            $btn.removeClass('favorite-active').addClass('favorite-inactive');
            $btn.html('<i class="layui-icon layui-icon-heart"></i> 添加到收藏夹');
            $btn.attr('title', '添加到收藏夹');
        }
    },

    // 更新所有收藏按钮状态
    updateFavoriteButtons: function() {
        var self = this;
        var buttonCount = $('.favorite-btn').length;
        
        if (buttonCount === 0) {
            return;
        }
        
        // 限制同时进行的AJAX请求数量
        var maxConcurrent = 3;
        var currentRequests = 0;
        var processedButtons = 0;
        
        $('.favorite-btn').each(function() {
            var $btn = $(this);
            var collegeName = decodeURIComponent($btn.data('college-name') || '');
            var majorName = decodeURIComponent($btn.data('major-name') || '');
            var majorCode = decodeURIComponent($btn.data('major-code') || '');
            var year = $btn.data('year') || new Date().getFullYear();
            
            if (!collegeName || !majorName || !majorCode) {
                processedButtons++;
                return;
            }
            
            // 如果当前请求数过多，延迟执行
            if (currentRequests >= maxConcurrent) {
                setTimeout(function() {
                    checkButtonStatus($btn, collegeName, majorCode, majorName, year);
                }, (processedButtons % maxConcurrent) * 200);
            } else {
                checkButtonStatus($btn, collegeName, majorCode, majorName, year);
            }
            
            function checkButtonStatus($btn, collegeName, majorCode, majorName, year) {
                currentRequests++;
                
                $.ajax({
                    url: 'word_sc_api.php',
                    type: 'POST',
                    data: {
                        action: 'check_favorite',
                        college_name: collegeName,
                        major_name: majorName,
                        major_code: majorCode,
                        year: year
                    },
                    dataType: 'json',
                    timeout: 5000,
                    success: function(response) {
                        if (response.success) {
                            self.updateButtonState($btn, response.is_favorite);
                        }
                    },
                    error: function(xhr, status, error) {
                        // 网络错误时，尝试从按钮的data属性判断状态
                        var isActive = $btn.hasClass('favorite-active');
                    },
                    complete: function() {
                        currentRequests--;
                        processedButtons++;
                    }
                });
            }
        });
    },

    // 更新收藏数量显示
    updateFavoriteCount: function() {
        // 避免重复请求
        if (this._updatingCount) {
            return;
        }
        
        this._updatingCount = true;
        
        $.ajax({
            url: 'word_sc_api.php',
            type: 'POST',
            data: {action: 'get_favorite_count'},
            dataType: 'json',
            timeout: 3000,
            success: function(response) {
                if (response.success) {
                    // 更新页面上的收藏数量显示
                    $('.favorite-count').each(function() {
                        var $element = $(this);
                        var type = $element.data('type');
                        if (type === 'major') {
                            $element.text(response.count);
                        }
                    });
                }
            },
            error: function() {
                // 更新收藏数量失败
            },
            complete: function() {
                // 重置标志，允许下次请求
                setTimeout(function() {
                    favoriteSystem._updatingCount = false;
                }, 1000);
            }
        });
    },

    // 检查新生成的收藏按钮状态
    checkNewFavoriteButtons: function() {
        setTimeout(function() {
            favoriteSystem.updateFavoriteButtons();
        }, 50);
    },

    // 创建收藏按钮HTML（重构版）
    createFavoriteButton: function(collegeInfo, majorInfo) {
        // 检查用户是否已登录
        if (!window.userLoggedIn || !window.dlusername || window.dlusername.trim() === '') {
            return '';
        }
        
        var collegeName = collegeInfo['院校名称'] || '';
        var majorName = majorInfo['专业名称'] || '';
        var majorCode = majorInfo['专业代号'] || '';
        var year = window.queryYear || new Date().getFullYear();
        
        if (!collegeName || !majorName || !majorCode) {
            return '';
        }
        
        // 获取科类和批次信息
        var subjectType = window.currentSubjectType || ''; // 从全局变量获取当前科类
        var batch = window.currentBatch || ''; // 从全局变量获取当前批次
        
        var btnHtml = '<div class="favorite-btn favorite-inactive" ' +
                     'data-college-name="' + collegeName + '" ' +
                     'data-major-name="' + majorName + '" ' +
                     'data-year="' + year + '" ' +
                     'data-college-code="' + (majorInfo['院校代号']) + '" ' +
                     'data-subject-type="' + (majorInfo['科类']) + '" ' +
                     'data-batch="' + (majorInfo['批次']) + '" ' +
                     'data-major-group="' + (majorInfo['专业组名称'] || '') + '" ' +
                     'data-major-group-code="' + (majorInfo['专业组代号'] || '') + '" ' +
                     'data-major-code="' + (majorInfo['专业代号'] || '') + '" ' +
                     'data-plan-count="' + (majorInfo['计划数'] || '') + '" ' +
                     'data-fee="' + (majorInfo['收费标准'] || '') + '" ' +
                     'data-subject-req="' + (majorInfo['选科要求'] || '') + '" ' +
                     'data-major-note="' + (majorInfo['专业备注'] || '') + '" ' +
                     'title="点击添加到收藏夹">' +
                     '<i class="layui-icon layui-icon-heart"></i> 添加到收藏夹</div>';
        
        return btnHtml;
    },

    // 获取院校信息（从yxinfo表）
    getCollegeInfo: function(collegeName, callback) {
        if (!collegeName) {
            if (callback) callback(null);
            return;
        }
        
        $.ajax({
            url: 'word_sc_api.php',
            type: 'POST',
            data: {
                action: 'get_college_info',
                college_name: collegeName
            },
            dataType: 'json',
            timeout: 5000,
            success: function(response) {
                if (response.success && callback) {
                    callback(response.data);
                } else if (callback) {
                    callback(null);
                }
            },
            error: function() {
                if (callback) callback(null);
            }
        });
    },

    // 批量删除收藏
    batchRemove: function(favoriteIds, callback) {
        if (!favoriteIds || favoriteIds.length === 0) {
            layer.msg('请选择要删除的收藏', {icon: 2});
            return;
        }
        
        layer.confirm('确定要删除选中的收藏吗？', {
            icon: 3,
            title: '确认删除'
        }, function(index) {
            layer.close(index);
            
            $.ajax({
                url: 'word_sc_api.php',
                type: 'POST',
                data: {
                    action: 'batch_remove',
                    favorite_ids: favoriteIds
                },
                dataType: 'json',
                timeout: 10000,
                success: function(response) {
                    if (response.success) {
                        layer.msg(response.message, {icon: 1});
                        if (callback) callback(true);
                    } else {
                        layer.msg(response.message || '批量删除失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请稍后再试', {icon: 2});
                }
            });
        });
    },

    // 清空收藏夹
    clearAll: function(callback) {
        layer.confirm('确定要清空整个收藏夹吗？此操作不可恢复！', {
            icon: 3,
            title: '确认清空'
        }, function(index) {
            layer.close(index);
            
            $.ajax({
                url: 'word_sc_api.php',
                type: 'POST',
                data: {
                    action: 'clear_all'
                },
                dataType: 'json',
                timeout: 10000,
                success: function(response) {
                    if (response.success) {
                        layer.msg(response.message, {icon: 1});
                        if (callback) callback(true);
                    } else {
                        layer.msg(response.message || '清空收藏夹失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请稍后再试', {icon: 2});
                }
            });
        });
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    // 检查用户登录状态
    if (typeof window.userLoggedIn === 'undefined') {
        window.userLoggedIn = typeof window.dlusername !== 'undefined' && 
                              window.dlusername && 
                              window.dlusername.trim() !== '';
    }
    
    // 初始化收藏系统
    favoriteSystem.init();
});

// 导出收藏功能到全局作用域
window.addFavoriteButton = function(collegeInfo, majorInfo) {
    return favoriteSystem.createFavoriteButton(collegeInfo, majorInfo);
};

// 设置全局变量（用于收藏按钮创建）
window.setCurrentQueryInfo = function(subjectType, batch) {
    window.currentSubjectType = subjectType;
    window.currentBatch = batch;
};
