behavior: url(border-radius.htc);

body,td,th {font-size:1em;font: normal 100% Helvetica, Arial, sans-serif;}
a {font: normal 100% Helvetica, Arial, sans-serif;}color: #333333;}
a:active {text-decoration: none;color: #CC0000;}
body {margin-left: 0px;	margin-top: 0px;margin-right: 0px;margin-bottom: 0px;background-color: #f5f5f5;}

a:link{font-size:1em;color:#000000;text-decoration:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a:visited{font-size:1em;color:#000000;	text-decoration:none;}
a:hover{font-size:1em;color:#000000;text-decoration:none;}
a.white:link{font-size:1em;color:#ffffff;text-decoration:none;}
a.white:visited{font-size:1em;color:#ffffff;text-decoration:none;}
a.white:hover{font-size:1em;color:#ffffff;text-decoration:underline;}
h1 {font-size: 1em; }
p{margin:0;}
#page{font-size:0.9em;height:20px;padding:3px 3px;}
#page a{display:block;float:left;margin-right:2px;padding:3px 10px
;height:20px;border:1px #cccccc solid;background:#ffffff;text-decoration:none;color:#000000;font-size:1em;line-height:20px;border-radius: 5px;}
#page a:hover{color:#3eb1dd;border:1px #d5d5d5 solid;}
#page a.cur{border:none;background:#3eb1dd;border:1px #3eb1dd solid;color:#ffffff;}
#page p{float:left;padding:3px 10px;font-size:1em;height:20px;line-height:20px;color:#bbbbbb;border:1px #cccccc solid;background:#efefef;margin-right:2px;border-radius: 5px;}
#page p.pageRemark{border-style:none;background:none;margin-right:0px;padding:2px 0px;color:#666;}
#page p.pageRemarkb{color:red;}
#page p.pageEllipsis{border-style:none;background:none;padding:2px 0px;color:#3eb1dd;}
.bottr{float:right;} 
.pagecenter{width: 355px;overflow: hidden; margin:0 auto ;text-align:center;padding-top:10px;padding-bottom:10px;} 

.fdBonTel{font-size:0.8em;width:100%; height:50px; position:fixed; background:#f8f8f8; text-align:center; left:0; bottom:0; z-index:999; }
.titlebar{width:100%; height:40px; position:fixed;  text-align:center; left:0; top:0; z-index:999;}
.titlebar1{width:250px; height:40px; position:fixed; text-align:center;top:0;left:0;right:0;margin:auto;z-index:999;}
.wxzz{background:#000000;filter:alpha(Opacity=90);-moz-opacity:0.9;opacity: 0.9;left:0;top:0;position:fixed;height:100%;width:100%;overflow:hidden;z-index:10000;}

*:focus { outline: none; }
input[type="submit"],
input[type="reset"],
input[type="button"],
input[type="text"],
input[type="password"],
button { -webkit-appearance: none; }
input,select,textarea {font: normal 100% Helvetica, Arial, sans-serif; padding:1px;}
textarea {border:1px #e0e0e0 solid;border-radius: 2px;}
input:-webkit-autofill {-webkit-box-shadow: 0 0 0px 1000px white inset !important;-webkit-text-fill-color: #3eb1dd;}
select::-ms-expand { display: none; }
select {
/*Chrome和Firefox里面的边框是不一样的，所以复写了一下*/
height:26px;
border: solid 1px #e0e0e0;
/*很关键：将默认的select选择框样式清除*/
appearance:none;
-moz-appearance:none;
-webkit-appearance:none;
/*在选择框的最右侧中间显示小箭头图片*/
background: url("images/arrow.png") no-repeat scroll right center transparent;
background-size:12px 12px;
/*为下拉小箭头留出一点位置，避免被文字覆盖*/
padding-right: 14px;
border-radius: 2px;
}
.inputg{height:21px;border:1px #e0e0e0 solid;border-radius: 2px;}
.inputk{width:95%;}
.inputk100{width:100%;}
.inputlong{width:80%;height:30px;border:1px #3eb1dd solid;border-radius: 2px;}
.btn{display: inline-block;margin-top: 0px;padding: 12px 12px;border-radius: 5px;background-color: #3eb1dd;color: #ffffff;cursor: pointer;}
.btns{display: inline-block;margin-top: 0px;padding: 3px 8px;border-radius: 5px;background-color: #3eb1dd;color: #ffffff;cursor: pointer;}
.btnlong{width:80%;color:#ffffff;border:1px  #3eb1dd solid;border-radius: 5px;background-color: #3eb1dd;padding: 12px 12px;}
.btnlong:hover{background-color: #3eb1dd;}
.btnlong:active{background-color: #3eb1dd;}
.btn:hover{background-color: #3eb1dd;}
.btn:active{background-color: #3eb1dd;}
.titlebtn{width:98%;padding: 4px 4px;background-color: #ffffff;}
.titlebtn:hover{background-color: #ffffff;}
.btnrmzy{display:inline-block;width:100%;padding:0px;background-color: #ffffff;cursor:pointer;}
.btnrmzy:hover{background-color: #ffffff;}
.titlebtn1{background-color: #efefef;}

.inbtn{border: none;}
.bubtn{border: none;}
.bgbtn02 img{margin-bottom: -9px;margin-right: 8px;}
.btnbg{width:79px;background: url(images/btnbg.png) no-repeat center;margin-bottom: -9px;margin-right: 8px;}
.btn_xan{color:#ffffff;border:1px #3eb1dd solid;border-radius: 15px;background-color: #3eb1dd;padding: 2px 8px 2px 8px;}

.myusername{
    background: url(images/username.png)no-repeat 10px ;
    width: 230px;
    height: 35px;
    border-radius:5px;
    border: 1px solid #3eb1dd;
    box-shadow: 1px 1px 3px #cccccc;
    padding-left: 40px;
}
.myloginbtn{width:270px;height: 42px;color:#ffffff;	border:1px #3eb1dd solid;border-radius: 5px;background-color: #3eb1dd;box-shadow: 1px 1px 3px #cccccc;}
.tabradl{border-top-left-radius:5px;border-bottom-left-radius:5px;overflow:hidden;}
.tabradr{border-top-right-radius:5px;border-bottom-right-radius:5px;overflow:hidden;}
.tabradt{border-radius:10px 10px 0px 0px;overflow:hidden;}
.tabyy{box-shadow: 1px 1px 1px #ffffff;}

.titlebarfont{text-shadow: 0px 1px 0px #000000;color:#efefef;font-weight: bold;}

.right-bottom
{
	width: 35px;
	height:35px;
	position: fixed;/*这是必须的*/
	z-index: 999;
	left:46%;/*这是必须的*/
	top:88%;/*这是必须的*/
	background:transparent;
	font-size:0.8em;
	color:#3eb1dd;
	text-align:center;
}

.circle {
border-radius: 50%;
width: 100px;
height: 100px; 
line-height:100px;  
text-align:center;
margin: 0 auto;
/* 画圆宽度和高度需要相等 */
}

/*箭头向上*/
.arrow-up {
    width:0; 
    height:0; 
    border-left:15px solid transparent;
    border-right:15px solid transparent;
    border-bottom:15px solid #ffffff;
}
.arrow-down {
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 5px solid #ffffff;
 }
 
 /*圆形图片*/
#div_tx img{
	border-radius:50%;
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	border: 3px solid #e5e5e5;
	overflow: hidden;
}
